#!/usr/bin/env python3
"""
Debug script to examine the structure of detailed job API responses
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from naukri_api_scraper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>raper
import json

def debug_job_details():
    """Debug the detailed job API response structure"""
    print("🔍 Debugging Job Details API Response Structure")
    print("=" * 60)
    
    # Initialize scraper
    scraper = NaukriAPIScraper()
    
    # Get a job ID from search first
    search_result = scraper.search_jobs("data scientist", "bangalore", 1, detailed=False)
    
    if search_result.get('scraped_jobs'):
        job_id = search_result['scraped_jobs'][0].get('job_id')
        print(f"🆔 Testing with job ID: {job_id}")
        print()
        
        # Get detailed info
        detailed_info = scraper.get_job_details(job_id)
        
        if detailed_info:
            print("✅ Successfully fetched detailed info")
            print(f"📊 Top-level keys: {list(detailed_info.keys())}")
            print()
            
            # Examine each top-level key
            for key, value in detailed_info.items():
                print(f"🔑 Key: '{key}'")
                print(f"   Type: {type(value)}")
                
                if isinstance(value, dict):
                    print(f"   Dict keys: {list(value.keys())}")
                    if key == 'jobDetails':
                        print(f"   📋 Job Details structure:")
                        for jd_key, jd_value in value.items():
                            print(f"      - {jd_key}: {type(jd_value)} = {str(jd_value)[:100]}...")
                elif isinstance(value, list):
                    print(f"   List length: {len(value)}")
                    if len(value) > 0:
                        print(f"   First item type: {type(value[0])}")
                        if isinstance(value[0], dict):
                            print(f"   First item keys: {list(value[0].keys())}")
                else:
                    print(f"   Value: {str(value)[:100]}...")
                print()
            
            # Save full response to file for detailed inspection
            with open('debug_job_response.json', 'w') as f:
                json.dump(detailed_info, f, indent=2, ensure_ascii=False)
            print("💾 Full response saved to 'debug_job_response.json'")
            
        else:
            print("❌ Failed to fetch detailed info")
    else:
        print("❌ No jobs found in search")

if __name__ == "__main__":
    debug_job_details()
