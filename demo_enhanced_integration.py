#!/usr/bin/env python3
"""
Demo script showing the Enhanced Naukri Scraper integration
"""

import requests
import json
import time
from datetime import datetime

def test_enhanced_naukri_integration():
    """Test the complete enhanced Naukri integration"""
    
    print("🚀 Enhanced Naukri Scraper Integration Demo")
    print("=" * 60)
    print()
    
    base_url = "http://localhost:8004"
    
    # Test 1: Health Check
    print("1️⃣ Testing Health Check...")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ Server is healthy!")
            print(f"   📊 Features: {', '.join(health_data.get('features', {}).keys())}")
            print(f"   🔗 Endpoints: {len(health_data.get('endpoints', {}))} available")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        print("💡 Make sure to run: python3 start_server.py")
        return
    
    print()
    
    # Test 2: Enhanced Search with Detailed Mode
    print("2️⃣ Testing Enhanced Search (Detailed Mode)...")
    search_payload = {
        "job_title": "python developer",
        "location": "mumbai", 
        "num_jobs": 2,
        "detailed": True
    }
    
    print(f"   📤 Request: {json.dumps(search_payload, indent=2)}")
    print("   ⏳ Searching... (this may take 20-30 seconds)")
    
    start_time = time.time()
    try:
        response = requests.post(
            f"{base_url}/scrape", 
            json=search_payload, 
            timeout=60
        )
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Search completed in {end_time - start_time:.1f} seconds")
            print(f"   📊 Found {data.get('total_scraped', 0)} jobs out of {data.get('api_total', 0)} available")
            
            # Show detailed job information
            if data.get('scraped_jobs'):
                job = data['scraped_jobs'][0]  # First job
                print(f"   📋 Sample Job Details:")
                print(f"      🆔 ID: {job.get('job_id', 'N/A')}")
                print(f"      📝 Title: {job.get('title', 'N/A')}")
                print(f"      🏢 Company: {job.get('company_name', 'N/A')}")
                print(f"      📍 Location: {job.get('location', 'N/A')}")
                print(f"      💰 Salary: {job.get('salary', 'N/A')}")
                print(f"      🎯 Experience: {job.get('experience', 'N/A')}")
                print(f"      👔 Role: {job.get('role', 'N/A')}")
                print(f"      🏭 Industry: {job.get('industry', 'N/A')}")
                print(f"      ⚙️ Function: {job.get('function', 'N/A')}")
                print(f"      💼 Job Type: {job.get('job_type', 'N/A')}")
                print(f"      👥 Applicants: {job.get('applicants_count', 'N/A')}")
                print(f"      🛠️ Skills: {len(job.get('skills', []))} skills")
                print(f"      📄 Description: {len(job.get('job_description', ''))} characters")
                print(f"      🏢 Company Info: {len(job.get('company_description', ''))} characters")
                
                # Test specific job details endpoint
                job_id = job.get('job_id')
                if job_id:
                    print()
                    print("3️⃣ Testing Job Details Endpoint...")
                    details_response = requests.post(
                        f"{base_url}/job-details",
                        json={"job_id": job_id},
                        timeout=30
                    )
                    
                    if details_response.status_code == 200:
                        details_data = details_response.json()
                        print(f"✅ Job details fetched successfully")
                        print(f"   📊 Data completeness: {sum(1 for v in details_data.values() if v) / len(details_data) * 100:.1f}%")
                    else:
                        print(f"❌ Job details failed: {details_response.status_code}")
        else:
            print(f"❌ Search failed: {response.status_code}")
            print(f"   📄 Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Search error: {e}")
    
    print()
    print("🎉 Demo completed!")
    print()
    print("💡 Frontend Integration:")
    print("   1. Install Node.js if not already installed")
    print("   2. cd web-portal && npm install && npm run dev")
    print("   3. Open http://localhost:3000")
    print("   4. Select 'Naukri' tab and search for jobs")
    print("   5. Enjoy comprehensive job data with rich UI!")

if __name__ == "__main__":
    test_enhanced_naukri_integration()
