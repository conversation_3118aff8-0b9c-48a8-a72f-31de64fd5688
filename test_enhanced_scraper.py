#!/usr/bin/env python3
"""
Test script for the enhanced Naukri scraper with detailed job information
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from naukri_api_scraper import NaukriAP<PERSON><PERSON>raper
import json

def test_basic_search():
    """Test basic search without detailed information"""
    print("🔍 Testing BASIC search (fast, less detailed)...")
    print("-" * 50)
    
    scraper = NaukriAPIScraper()
    result = scraper.search_jobs("python developer", "mumbai", 2, detailed=False)
    
    print(f"✅ Found {result.get('total_scraped', 0)} jobs")
    if result.get('scraped_jobs'):
        first_job = result['scraped_jobs'][0]
        print(f"📋 Sample job: {first_job.get('title')} at {first_job.get('company_name')}")
        print(f"📝 Description length: {len(first_job.get('job_description', ''))} characters")
    print()

def test_detailed_search():
    """Test detailed search with comprehensive information"""
    print("🔍 Testing DETAILED search (slower, comprehensive)...")
    print("-" * 50)
    
    scraper = NaukriAPIScraper()
    result = scraper.search_jobs("python developer", "mumbai", 2, detailed=True)
    
    print(f"✅ Found {result.get('total_scraped', 0)} jobs")
    if result.get('scraped_jobs'):
        first_job = result['scraped_jobs'][0]
        print(f"📋 Sample job: {first_job.get('title')} at {first_job.get('company_name')}")
        print(f"📝 Description length: {len(first_job.get('job_description', ''))} characters")
        print(f"🏢 Company description: {len(first_job.get('company_description', ''))} characters")
        print(f"🛠️ Skills count: {len(first_job.get('skills', []))}")
    print()

def test_specific_job_details():
    """Test getting details for a specific job ID"""
    print("🔍 Testing SPECIFIC job details...")
    print("-" * 50)
    
    # First get a job ID from basic search
    scraper = NaukriAPIScraper()
    search_result = scraper.search_jobs("data analyst", "pune", 1, detailed=False)
    
    if search_result.get('scraped_jobs'):
        job_id = search_result['scraped_jobs'][0].get('job_id')
        if job_id:
            print(f"🆔 Testing job ID: {job_id}")
            detailed_info = scraper.get_job_details(job_id)
            
            if detailed_info:
                print(f"✅ Successfully fetched detailed info")
                print(f"📊 Response keys: {list(detailed_info.keys())}")
                if detailed_info.get('jdJSON'):
                    print(f"📋 Has comprehensive job description: Yes")
                else:
                    print(f"📋 Has comprehensive job description: No")
            else:
                print("❌ Failed to fetch detailed info")
        else:
            print("❌ No job ID found in search results")
    else:
        print("❌ No jobs found in search")
    print()

if __name__ == "__main__":
    print("🚀 Enhanced Naukri Scraper Test Suite")
    print("=" * 60)
    print()
    
    try:
        # Test 1: Basic search
        test_basic_search()
        
        # Test 2: Detailed search  
        test_detailed_search()
        
        # Test 3: Specific job details
        test_specific_job_details()
        
        print("🎉 All tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
