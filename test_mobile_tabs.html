<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Tabs Test - Job Scraper Portal</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-tabs {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            padding: 12px;
            border: 1px solid rgba(147, 51, 234, 0.2);
            margin-bottom: 20px;
        }
        
        .tabs-container {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding: 8px;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }
        
        .tabs-container::-webkit-scrollbar {
            display: none;
        }
        
        .tab-button {
            background: rgba(147, 51, 234, 0.2);
            color: rgba(147, 51, 234, 0.8);
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            white-space: nowrap;
            flex-shrink: 0;
            min-width: fit-content;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .tab-button.active {
            background: linear-gradient(to right, #9333ea, #a855f7);
            color: white;
            box-shadow: 0 4px 12px rgba(147, 51, 234, 0.25);
            transform: scale(1.05);
        }
        
        .tab-button:hover:not(.active) {
            background: rgba(147, 51, 234, 0.3);
            color: rgba(147, 51, 234, 1);
        }
        
        .scroll-hint {
            text-align: center;
            margin-top: 8px;
            font-size: 11px;
            color: rgba(147, 51, 234, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .tab-indicators {
            display: flex;
            justify-content: center;
            gap: 4px;
            margin-top: 8px;
        }
        
        .indicator {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: rgba(147, 51, 234, 0.4);
            transition: all 0.3s ease;
        }
        
        .indicator.active {
            background: #a855f7;
            transform: scale(1.25);
        }
        
        .info-card {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .success-badge {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .feature-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 30px; font-size: 1.5rem;">
            📱 Mobile Tabs - All 7 Portals Visible!
        </h1>
        
        <div class="demo-tabs">
            <div class="tabs-container" id="tabsContainer">
                <button class="tab-button active" data-tab="foundit">Foundit.in</button>
                <button class="tab-button" data-tab="glassdoor">Glassdoor</button>
                <button class="tab-button" data-tab="simplyhired">SimplyHired</button>
                <button class="tab-button" data-tab="ziprecruiter">ZipRecruiter</button>
                <button class="tab-button" data-tab="linkedin">LinkedIn</button>
                <button class="tab-button" data-tab="indeed">Indeed</button>
                <button class="tab-button" data-tab="naukri">Naukri</button>
            </div>
            
            <div class="scroll-hint">
                <span>👈</span>
                <span>Swipe to see all 7 portals</span>
                <span>👉</span>
            </div>
            
            <div class="tab-indicators">
                <div class="indicator active" data-indicator="foundit"></div>
                <div class="indicator" data-indicator="glassdoor"></div>
                <div class="indicator" data-indicator="simplyhired"></div>
                <div class="indicator" data-indicator="ziprecruiter"></div>
                <div class="indicator" data-indicator="linkedin"></div>
                <div class="indicator" data-indicator="indeed"></div>
                <div class="indicator" data-indicator="naukri"></div>
            </div>
        </div>
        
        <div class="info-card">
            <div class="success-badge">✅ FIXED</div>
            <h2 style="margin: 0 0 15px 0; font-size: 1.2rem;">Mobile Tab Navigation</h2>
            
            <ul class="feature-list">
                <li>All 7 job portals visible and accessible</li>
                <li>Horizontal scrolling with smooth behavior</li>
                <li>Auto-scroll active tab into view</li>
                <li>Visual indicators showing current tab</li>
                <li>Touch-friendly button sizes</li>
                <li>Hidden scrollbar for clean appearance</li>
                <li>Swipe gesture support</li>
            </ul>
            
            <div style="margin-top: 20px; padding: 15px; background: rgba(147, 51, 234, 0.1); border-radius: 8px; border-left: 4px solid #a855f7;">
                <strong>How it works:</strong><br>
                • On mobile: Horizontal scrollable tabs<br>
                • On desktop: Grid layout with all tabs visible<br>
                • Active tab automatically scrolls into view<br>
                • Visual feedback with indicators and scaling
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding: 20px; background: rgba(0,0,0,0.2); border-radius: 12px;">
            <h3 style="color: #ffd700; margin-bottom: 10px;">🎉 Problem Solved!</h3>
            <p style="margin: 0; opacity: 0.9;">
                All 7 job portals are now easily accessible on mobile devices with smooth scrolling and visual feedback.
            </p>
        </div>
    </div>
    
    <script>
        // Demo functionality
        const tabs = document.querySelectorAll('.tab-button');
        const indicators = document.querySelectorAll('.indicator');
        const container = document.getElementById('tabsContainer');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabKey = tab.dataset.tab;
                
                // Update active states
                tabs.forEach(t => t.classList.remove('active'));
                indicators.forEach(i => i.classList.remove('active'));
                
                tab.classList.add('active');
                document.querySelector(`[data-indicator="${tabKey}"]`).classList.add('active');
                
                // Scroll active tab into view
                setTimeout(() => {
                    tab.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest',
                        inline: 'center'
                    });
                }, 100);
            });
        });
        
        // Auto-scroll demo
        let currentIndex = 0;
        setInterval(() => {
            currentIndex = (currentIndex + 1) % tabs.length;
            tabs[currentIndex].click();
        }, 3000);
    </script>
</body>
</html>
