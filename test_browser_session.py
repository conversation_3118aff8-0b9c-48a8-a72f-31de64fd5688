#!/usr/bin/env python3
"""
Test script to verify browser-based session initialization works
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from naukri_api_scraper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>raper
import json

def test_browser_session():
    """Test the browser-based session initialization"""
    print("Testing browser-based session initialization...")
    
    try:
        # Create scraper instance
        scraper = NaukriAPIScraper()
        
        # Test search
        result = scraper.search_jobs("data analyst", "pune", 3)
        
        # Print results
        print("\n" + "="*50)
        print("SEARCH RESULTS:")
        print("="*50)
        print(json.dumps(result, indent=2))
        
        # Check if we got real data or sample data
        if result.get('scraped_jobs') and len(result['scraped_jobs']) > 0:
            first_job = result['scraped_jobs'][0]
            if first_job.get('job_id') == 'sample_001':
                print("\n❌ Still getting sample data - reCAPTCHA protection active")
            else:
                print("\n✅ Got real job data - browser session worked!")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")

if __name__ == "__main__":
    test_browser_session()
