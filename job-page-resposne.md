<!DOCTYPE html>
<html lang="en" class="__className_151f7e">
    <head>
        <meta charSet="utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <link rel="preload" as="image" href="https://media.foundit.in/public/core/images/founditLogo-primary.png"/>
        <link rel="preload" as="image" href="https://media.foundit.in/public/core/images/appstore.svg"/>
        <link rel="preload" as="image" href="https://media.foundit.in/public/core/images/playstore.svg"/>
        <link rel="preload" as="image" href="https://media.monsterindia.com/logos/xeft_heptarcinx/jdlogo.gif"/>
        <link rel="stylesheet" href="https://media.foundit.in/public/core/_next/static/css/d97ec4ca882e9b24.css" data-precedence="next"/>
        <link rel="stylesheet" href="https://media.foundit.in/public/core/_next/static/css/be9e52fd61916c77.css" data-precedence="next"/>
        <link rel="stylesheet" href="https://media.foundit.in/public/core/_next/static/css/c06e7e4de5c17984.css" data-precedence="next"/>
        <link rel="stylesheet" href="https://media.foundit.in/public/core/_next/static/css/043491062fceef02.css" data-precedence="next"/>
        <link rel="stylesheet" href="https://media.foundit.in/public/core/_next/static/css/13dbffa19191361b.css" data-precedence="next"/>
        <link rel="preload" as="script" fetchPriority="low" href="https://media.foundit.in/public/core/_next/static/chunks/webpack-37cdbaff12b6db06.js"/>
        <script src="https://media.foundit.in/public/core/_next/static/chunks/main-app-a1b429d5d88adb22.js" async=""></script>
        <script src="https://media.foundit.in/public/core/_next/static/chunks/app/global-error-3e48fa67b6e1c02e.js" async=""></script>
        <script src="https://media.foundit.in/public/core/_next/static/chunks/app/layout-a565af9c1482ec4e.js" async=""></script>
        <script src="https://media.foundit.in/public/core/_next/static/chunks/app/error-7ce573952a375044.js" async=""></script>
        <script src="https://media.foundit.in/public/core/_next/static/chunks/app/(Engagement)/job/%5B...slug%5D/page-8ded5f783839f2d3.js" async=""></script>
        <title>Python Developer Job for 5 - 7 Year of Exp In Heptarc Technology Solutions Private Limited Hyderabad / Secunderabad, Telangana,Bengaluru / Bangalore - ******** | foundit india </title>
        <meta name="description" content="Job Description For Python Developer Posted by Heptarc Technology Solutions Private Limited For Hyderabad / Secunderabad, Telangana,Bengaluru / Bangalore. Require 5 years Experience With Other Qualification. Apply Now To This And Other Similar Jobs ! "/>
        <meta name="robots" content="index, follow"/>
        <link rel="canonical" href="https://www.foundit.in/job/python-developer-heptarc-technology-solutions-private-limited-hyderabad-secunderabad-telangana-********"/>
        <meta property="og:title" content="Python Developer Job for 5 - 7 Year of Exp In Heptarc Technology Solutions Private Limited Hyderabad / Secunderabad, Telangana,Bengaluru / Bangalore - ******** | foundit india "/>
        <meta property="og:description" content="Job Description For Python Developer Posted by Heptarc Technology Solutions Private Limited For Hyderabad / Secunderabad, Telangana,Bengaluru / Bangalore. Require 5 years Experience With Other Qualification. Apply Now To This And Other Similar Jobs ! "/>
        <meta property="og:url" content="https://www.foundit.in/job/python-developer-heptarc-technology-solutions-private-limited-hyderabad-secunderabad-telangana-********"/>
        <meta property="og:site_name" content="foundit india"/>
        <meta property="og:type" content="website"/>
        <meta name="twitter:card" content="summary"/>
        <meta name="twitter:title" content="Python Developer Job for 5 - 7 Year of Exp In Heptarc Technology Solutions Private Limited Hyderabad / Secunderabad, Telangana,Bengaluru / Bangalore - ******** | foundit india "/>
        <meta name="twitter:description" content="Job Description For Python Developer Posted by Heptarc Technology Solutions Private Limited For Hyderabad / Secunderabad, Telangana,Bengaluru / Bangalore. Require 5 years Experience With Other Qualification. Apply Now To This And Other Similar Jobs ! "/>
        <link rel="icon" href="/home/<USER>" type="image/x-icon" sizes="16x16"/>
        <meta name="next-size-adjust"/>
        <link rel="preconnect" href="media.foundit.in" crossorigin="anonymous"/>
        <link rel="dns-prefetch" href="media.foundit.in"/>
        <link rel="preconnect" href="https://pagead2.googlesyndication.com/" crossorigin="anonymous"/>
        <link rel="dns-prefetch" href="https://pagead2.googlesyndication.com/"/>
        <link rel="preconnect" href="https://www.google-analytics.com/" crossorigin="anonymous"/>
        <link rel="dns-prefetch" href="https://www.google-analytics.com/"/>
        <link rel="preconnect" href="https://connect.facebook.net/" crossorigin="anonymous"/>
        <link rel="dns-prefetch" href="https://connect.facebook.net/"/>
        <link rel="preconnect" href="https://www.googletagmanager.com/" crossorigin="anonymous"/>
        <link rel="dns-prefetch" href="https://www.googletagmanager.com/"/>
        <link rel="preconnect" href="https://www.googletagservices.com/" crossorigin="anonymous"/>
        <link rel="dns-prefetch" href="https://www.googletagservices.com/"/>
        <link rel="preconnect" href="https://googleads.g.doubleclick.net/" crossorigin="anonymous"/>
        <link rel="dns-prefetch" href="https://googleads.g.doubleclick.net/"/>
        <link rel="preload" href="https://media.foundit.in/public/core/_next/static/media/103fb0c975d13c4f-s.p.woff2" as="font" type="font/woff" crossorigin="anonymous"/>
        <link rel="preload" href="https://media.foundit.in/public/core/_next/static/media/slick.653a4cbb.woff" as="font" type="font/woff" crossorigin="anonymous"/>
        <meta name="facebook-domain-verification" content="d98wmjxybl8zhqmj01l5uy9kxtxk19"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <meta http-equiv="X-UA-Compatible" content="IE=9; IE=8; IE=7; IE=EDGE; chrome=1"/>
        <script src="https://media.foundit.in/public/core/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script>
        <script>
            !function(e) {
                var n = "https://s.go-mpulse.net/boomerang/";
                if ("False" == "True")
                    e.BOOMR_config = e.BOOMR_config || {},
                    e.BOOMR_config.PageParams = e.BOOMR_config.PageParams || {},
                    e.BOOMR_config.PageParams.pci = !0,
                    n = "https://s2.go-mpulse.net/boomerang/";
                if (window.BOOMR_API_key = "2PT3Q-JAT3X-EBX46-NT4FH-M8PHW",
                function() {
                    function e() {
                        if (!r) {
                            var e = document.createElement("script");
                            e.id = "boomr-scr-as",
                            e.src = window.BOOMR.url,
                            e.async = !0,
                            o.appendChild(e),
                            r = !0
                        }
                    }
                    function t(e) {
                        r = !0;
                        var n, t, a, i, d = document, O = window;
                        if (window.BOOMR.snippetMethod = e ? "if" : "i",
                        t = function(e, n) {
                            var t = d.createElement("script");
                            t.id = n || "boomr-if-as",
                            t.src = window.BOOMR.url,
                            BOOMR_lstart = (new Date).getTime(),
                            e = e || d.body,
                            e.appendChild(t)
                        }
                        ,
                        !window.addEventListener && window.attachEvent && navigator.userAgent.match(/MSIE [67]\./))
                            return window.BOOMR.snippetMethod = "s",
                            void t(o, "boomr-async");
                        a = document.createElement("IFRAME"),
                        a.src = "about:blank",
                        a.title = "",
                        a.role = "presentation",
                        a.loading = "eager",
                        i = (a.frameElement || a).style,
                        i.width = 0,
                        i.height = 0,
                        i.border = 0,
                        i.display = "none",
                        o.appendChild(a);
                        try {
                            O = a.contentWindow,
                            d = O.document.open()
                        } catch (_) {
                            n = document.domain,
                            a.src = "javascript:var d=document.open();d.domain='" + n + "';void 0;",
                            O = a.contentWindow,
                            d = O.document.open()
                        }
                        if (n)
                            d._boomrl = function() {
                                this.domain = n,
                                t()
                            }
                            ,
                            d.write("<bo" + "dy onload='document._boomrl();'>");
                        else if (O._boomrl = function() {
                            t()
                        }
                        ,
                        O.addEventListener)
                            O.addEventListener("load", O._boomrl, !1);
                        else if (O.attachEvent)
                            O.attachEvent("onload", O._boomrl);
                        d.close()
                    }
                    function a(e) {
                        window.BOOMR_onload = e && e.timeStamp || (new Date).getTime()
                    }
                    if (!window.BOOMR || !window.BOOMR.version && !window.BOOMR.snippetExecuted) {
                        window.BOOMR = window.BOOMR || {},
                        window.BOOMR.snippetStart = (new Date).getTime(),
                        window.BOOMR.snippetExecuted = !0,
                        window.BOOMR.snippetVersion = 14,
                        window.BOOMR.url = n + "2PT3Q-JAT3X-EBX46-NT4FH-M8PHW";
                        var i = document.currentScript || document.getElementsByTagName("script")[0]
                          , o = i.parentNode
                          , r = !1
                          , d = document.createElement("link");
                        if (d.relList && "function" == typeof d.relList.supports && d.relList.supports("preload") && "as"in d)
                            window.BOOMR.snippetMethod = "p",
                            d.href = window.BOOMR.url,
                            d.rel = "preload",
                            d.as = "script",
                            d.addEventListener("load", e),
                            d.addEventListener("error", function() {
                                t(!0)
                            }),
                            setTimeout(function() {
                                if (!r)
                                    t(!0)
                            }, 3e3),
                            BOOMR_lstart = (new Date).getTime(),
                            o.appendChild(d);
                        else
                            t(!1);
                        if (window.addEventListener)
                            window.addEventListener("load", a, !1);
                        else if (window.attachEvent)
                            window.attachEvent("onload", a)
                    }
                }(),
                "".length > 0)
                    if (e && "performance"in e && e.performance && "function" == typeof e.performance.setResourceTimingBufferSize)
                        e.performance.setResourceTimingBufferSize();
                !function() {
                    if (BOOMR = e.BOOMR || {},
                    BOOMR.plugins = BOOMR.plugins || {},
                    !BOOMR.plugins.AK) {
                        var n = "" == "true" ? 1 : 0
                          , t = ""
                          , a = "m73qpc3ikwkbo2ecfkjq-f-c4c83d695-clientnsv4-s.akamaihd.net"
                          , i = "false" == "true" ? 2 : 1
                          , o = {
                            "ak.v": "39",
                            "ak.cp": "1408286",
                            "ak.ai": parseInt("855481", 10),
                            "ak.ol": "0",
                            "ak.cr": 90,
                            "ak.ipv": 4,
                            "ak.proto": "h2",
                            "ak.rid": "522d0f0",
                            "ak.r": 29145,
                            "ak.a2": n,
                            "ak.m": "b",
                            "ak.n": "essl",
                            "ak.bpcip": "***********",
                            "ak.cport": 57692,
                            "ak.gh": "************",
                            "ak.quicv": "",
                            "ak.tlsv": "tls1.3",
                            "ak.0rtt": "",
                            "ak.0rtt.ed": "",
                            "ak.csrc": "-",
                            "ak.acc": "",
                            "ak.t": "1753361043",
                            "ak.ak": "hOBiQwZUYzCg5VSAfCLimQ==/73jynNGf/+H9S8GadGxupflj/laB9Qyl0RWvMU13DfQDTkHe6/bN9Hm+/VNoUdc8dIPUycvnQf4o/E0vjdXbGyJc6u2vAOcL3WFBz++VJcIZgxyJzb6p7gTRMgiTxR/Rcg1xN6scRwEK1u5mNqbm6611jmytykKHkYbVOERiG9LtTFhXp/2M3OMiaQ0qv//V8uWZfgVcNldr61Rh20tlnR9boBBaqh3wppN/82oe6PTdLWB/1njHcJcBtYnbM5+xZhHfvIjk8oYVlwRHp44HynCtVI3Eex+pYg9TpCS2LG79UjLCgLfAmtxW2/dXiEosOXu6Mwf18W5AfLH9mtrkaAC81SAblTIooLT2G872fapJ+USw9URVO4UjMG94ylKJRphRValXnGawoBoe+K7NdrAEWVdWd1l+aFaUUfkZ/w=",
                            "ak.pv": "172",
                            "ak.dpoabenc": "",
                            "ak.tf": i
                        };
                        if ("" !== t)
                            o["ak.ruds"] = t;
                        var r = {
                            i: !1,
                            av: function(n) {
                                var t = "http.initiator";
                                if (n && (!n[t] || "spa_hard" === n[t]))
                                    o["ak.feo"] = void 0 !== e.aFeoApplied ? 1 : 0,
                                    BOOMR.addVar(o)
                            },
                            rv: function() {
                                var e = ["ak.bpcip", "ak.cport", "ak.cr", "ak.csrc", "ak.gh", "ak.ipv", "ak.m", "ak.n", "ak.ol", "ak.proto", "ak.quicv", "ak.tlsv", "ak.0rtt", "ak.0rtt.ed", "ak.r", "ak.acc", "ak.t", "ak.tf"];
                                BOOMR.removeVar(e)
                            }
                        };
                        BOOMR.plugins.AK = {
                            akVars: o,
                            akDNSPreFetchDomain: a,
                            init: function() {
                                if (!r.i) {
                                    var e = BOOMR.subscribe;
                                    e("before_beacon", r.av, null, null),
                                    e("onbeacon", r.rv, null, null),
                                    r.i = !0
                                }
                                return this
                            },
                            is_complete: function() {
                                return !0
                            }
                        }
                    }
                }()
            }(window);
        </script>
    </head>
    <body class="overflow-x-hidden">
        <div id="seekerHeader" class="z-[999999] w-full py-2 md:py-3 relative top-0 border-b-darkKnight-200 border-b bg-surface-primary-normal lg:!sticky lg:bg-surface-primary-normal lg:shadow-lg">
            <div class="flex_between w-full max-w-[1208px] gap-3 px-4 md:container md:px-0 lg:gap-10">
                <div class="flex_between flex min-w-max gap-4">
                    <div class="flex_start h-9 grow cursor-pointer gap-5">
                        <img alt="foundit Logo" src="https://media.foundit.in/public/core/images/founditLogo-primary.png" class="company_logo w-auto" style="height:24px"/>
                    </div>
                    <div class="md:flex_around hidden h-9 gap-6">
                        <div class="md:flex_around hidden h-9 gap-6  md:hidden">
                            <div class="group relative ">
                                <div>
                                    <a href="/search/jobs" target="_self" class="main_link cursor-pointer  text-sm font-semibold decoration-2 underline-offset-4 group-hover:text-primary-200 group-hover:underline">Jobs</a>
                                </div>
                            </div>
                            <div class="group relative ">
                                <div>
                                    <a href="/career-services" target="_self" class="main_link cursor-pointer  text-sm font-semibold decoration-2 underline-offset-4 group-hover:text-primary-200 group-hover:underline">Services</a>
                                </div>
                                <div class="invisible_elem absolute z-50 mt-6 grid max-h-[500px] min-w-[228px] max-w-[1010px] translate-y-0 flex-wrap items-start gap-2 overflow-y-auto rounded-lg border-2 bg-white p-4 text-darkKnight-1000 opacity-0 shadow-xl transition-all duration-500 ease-in-out group-hover:visible group-hover:-translate-y-5 group-hover:opacity-100 -ml-48 grid-cols-[repeat(auto-fit,228px)]">
                                    <div class="h-[100%] w-[228px] pl-1 false ">
                                        <a href="/career-services/boost" target="_self" class="">
                                            <span class="w-[calc(100%_-_16px)] px-2 text-sm font-bold">Highlight</span>
                                        </a>
                                        <div class="flex list-none flex-col gap-2 pt-4">
                                            <a href="/career-services/featured-profile-services" target="_self" class="sub_link w-[calc(100%_-_16px)] rounded p-2 text-[13px] hover:cursor-pointer hover:bg-surface-primary-pressed hover:text-primary-200 ">Featured profile</a>
                                            <a href="/career-services/profile-highlighter" target="_self" class="sub_link w-[calc(100%_-_16px)] rounded p-2 text-[13px] hover:cursor-pointer hover:bg-surface-primary-pressed hover:text-primary-200 ">Profile highlighter</a>
                                            <a href="/career-services/career-booster" target="_self" class="sub_link w-[calc(100%_-_16px)] rounded p-2 text-[13px] hover:cursor-pointer hover:bg-surface-primary-pressed hover:text-primary-200 ">
                                                Career Booster<span class="ml-2 rounded-[18px]  px-2 py-0.5 text-[8px] bg-decorative-surface-04 text-secondary-orange-0">Best Seller</span>
                                            </a>
                                            <a href="/career-services/resume-services" target="_self" class="sub_link w-[calc(100%_-_16px)] rounded p-2 text-[13px] hover:cursor-pointer hover:bg-surface-primary-pressed hover:text-primary-200 ">Resume Writing</a>
                                            <a href="/career-services/premium-resume-builder" target="_self" class="sub_link w-[calc(100%_-_16px)] rounded p-2 text-[13px] hover:cursor-pointer hover:bg-surface-primary-pressed hover:text-primary-200 ">Resume builder</a>
                                            <a href="/career-services/linkedin-profile-makeover" target="_self" class="sub_link w-[calc(100%_-_16px)] rounded p-2 text-[13px] hover:cursor-pointer hover:bg-surface-primary-pressed hover:text-primary-200 ">Linkedin Makeover</a>
                                        </div>
                                    </div>
                                    <div class="h-[100%] w-[228px] pl-1 border-l border-[#E7E7EA] ">
                                        <a href="/career-services/prep" target="_self" class="">
                                            <span class="w-[calc(100%_-_16px)] px-2 text-sm font-bold">Prep</span>
                                        </a>
                                        <div class="flex list-none flex-col gap-2 pt-4">
                                            <a href="/career-services/mock-interview" target="_self" class="sub_link w-[calc(100%_-_16px)] rounded p-2 text-[13px] hover:cursor-pointer hover:bg-surface-primary-pressed hover:text-primary-200 ">Mock interview</a>
                                            <a href="/career-services/interview-preparation" target="_self" class="sub_link w-[calc(100%_-_16px)] rounded p-2 text-[13px] hover:cursor-pointer hover:bg-surface-primary-pressed hover:text-primary-200 ">Interview Preparation</a>
                                        </div>
                                    </div>
                                    <div class="h-[100%] w-[228px] pl-1 border-l border-[#E7E7EA] ">
                                        <a href="/career-services/learn" target="_self" class="">
                                            <span class="w-[calc(100%_-_16px)] px-2 text-sm font-bold">Learn</span>
                                        </a>
                                        <div class="flex list-none flex-col gap-2 pt-4">
                                            <a href="/career-services/online-degree-programs" target="_self" class="sub_link w-[calc(100%_-_16px)] rounded p-2 text-[13px] hover:cursor-pointer hover:bg-surface-primary-pressed hover:text-primary-200 ">Degree Programs</a>
                                            <a href="/career-services/certification-programs" target="_self" class="sub_link w-[calc(100%_-_16px)] rounded p-2 text-[13px] hover:cursor-pointer hover:bg-surface-primary-pressed hover:text-primary-200 ">Certification</a>
                                        </div>
                                    </div>
                                    <a href="/career-advice/" target="_self" class="sub_link w-[calc(100%_-_16px)] rounded p-2 text-[13px] hover:cursor-pointer hover:bg-surface-primary-pressed hover:text-primary-200 ">Career Advice</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hidden lg:inline-block">
                    <div class="flex h-full flex-col lg:inline-block lg:h-auto">
                        <div class="search_section_back mb-3 flex cursor-pointer items-center font-bold md:inline-block lg:hidden">
                            <svg viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
                                <path fill-rule="evenodd" d="M14.53 5.47c.******** 0 1.06L9.06 12l5.47 5.47a.75.75 0 1 1-1.06 1.06l-6-6a.75.75 0 0 1 0-1.06l6-6c.3-.3.77-.3 1.06 0Z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Search Jobs</span>
                        </div>
                        <form id="searchForm" class="relative grow  ">
                            <div class="flex h-auto w-full flex-col gap-4 border-none p-0 text-base shadow-none lg:flex lg:h-10 lg:flex-row lg:items-center lg:gap-0 lg:rounded-full lg:border lg:border-solid lg:border-gray-300 lg:bg-white lg:py-1 lg:pl-3 lg:pr-1 lg:text-sm lg:shadow-md">
                                <div class="searchBar_siblings search_componentWrapper lg:w-1/2 lg:p-0 false ">
                                    <div id="Desktop-skillsAutoComplete" class="searchComp skillSearch flex h-full items-center">
                                        <div class="flex h-full w-full items-center">
                                            <div class="input-container flex flex-col gap-1 w-full">
                                                <div class="relative flex w-full flex-col" tabindex="-1">
                                                    <input placeholder="Search by Skills, Company or Job Title" class="m-0 rounded-[4px] border border-darkKnight-200 p-3 text-sm font-normal outline-none focus:border-darkKnight-400 h-full w-full overflow-hidden text-ellipsis whitespace-nowrap border-none py-0 !pl-0 pr-3 false focus:outline-none input-box" id="Desktop-skillsAutoComplete--input" type="text" autoComplete="off" value=""/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="w-fit-content mx-2 flex h-full items-center">
                                            <span class="clear_values min-w-[10px] cursor-pointer"></span>
                                        </div>
                                        <div class="pointer-events-none absolute left-0 top-11 z-10 flex h-auto min-h-8 w-full items-center rounded-md bg-black  px-4 py-3 text-xs text-white opacity-0 transition-opacity duration-500 ease-in-out  false ">Please enter a job title/role/skill to search</div>
                                    </div>
                                </div>
                                <div class="searchBar_siblings search_componentWrapper lg:w-[24%] lg:min-w-[144px]  lg:pl-2 lg:pt-0">
                                    <div id="Desktop-locationAutoComplete" class="searchComp locationSearch flex h-full items-center">
                                        <div class="flex h-full w-full items-center">
                                            <div class="input-container flex flex-col gap-1 w-full">
                                                <div class="relative flex w-full flex-col" tabindex="-1">
                                                    <input placeholder="Location" class="m-0 rounded-[4px] border border-darkKnight-200 p-3 text-sm font-normal outline-none focus:border-darkKnight-400 h-full w-full overflow-hidden text-ellipsis whitespace-nowrap border-none py-0 !pl-0 pr-3 false focus:outline-none input-box" id="Desktop-locationAutoComplete--input" type="text" autoComplete="off" value=""/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="w-fit-content mx-2 flex h-full items-center">
                                            <span class="clear_values min-w-[10px] cursor-pointer"></span>
                                        </div>
                                        <div class="pointer-events-none absolute left-0 top-11 z-10 flex h-auto min-h-8 w-full items-center rounded-md bg-black  px-4 py-3 text-xs text-white opacity-0 transition-opacity duration-500 ease-in-out  false ">Please enter a job title/role/skill to search</div>
                                    </div>
                                </div>
                                <div class="searchBar_siblings lg:h-full lg:w-[24%] lg:min-w-[144px] lg:rounded-none lg:border-none lg:pl-2 lg:pt-0 lg:shadow-none">
                                    <div id="Desktop-expAutoComplete" class="searchComp expSearch flex h-full items-center">
                                        <div class="flex h-full w-full items-center">
                                            <div class="input-container flex flex-col gap-1 w-full">
                                                <div class="relative flex w-full flex-col" tabindex="-1">
                                                    <input placeholder="Experience" class="m-0 rounded-[4px] border border-darkKnight-200 p-3 text-sm font-normal outline-none focus:border-darkKnight-400 h-full w-full overflow-hidden text-ellipsis whitespace-nowrap border-none py-0 !pl-0 pr-3 false focus:outline-none input-box" id="Desktop-expAutoComplete--input" type="text" autoComplete="off" readOnly="" value=""/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="w-fit-content mx-2 flex h-full items-center">
                                            <span class="dropdown_icon transform transition-transform duration-200 ease-in-out ">
                                                <svg fill="#777584" viewBox="0 0 24 24" width="16" height="16">
                                                    <path fill-rule="evenodd" d="M5.47 9.47c.3-.3.77-.3 1.06 0L12 14.94l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6 6c-.3.3-.77.3-1.06 0l-6-6a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path>
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="pointer-events-none absolute left-0 top-11 z-10 flex h-auto min-h-8 w-full items-center rounded-md bg-black  px-4 py-3 text-xs text-white opacity-0 transition-opacity duration-500 ease-in-out  false ">Please enter a job title/role/skill to search</div>
                                        <div id="searchDropDown" class="absolute left-0 top-12 z-[1] max-h-36 w-full overflow-y-scroll rounded-[8px] border border-solid border-gray-300 bg-white lg:top-10 hidden">
                                            <ul>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5">
                                                        Fresher
                                                        <!-- -->
                                                        <span class="text-xs opacity-50">(&lt;1 year)</span>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                                <li class="dropDown_options text-dark-purple cursor-pointer border-b-0 py-1 pl-3 pr-2 text-sm font-normal leading-4 hover:bg-purple-100">
                                                    <span class="leading-5
        undefined">
                                                        <strong style="color:#6e00be"></strong>
                                                    </span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="inline-block w-full lg:hidden ">
                                    <div>Recent Searches</div>
                                    <div class="mt-2">
                                        <ul>
                                            <li class="mt-1 flex cursor-pointer items-center gap-1">
                                                <svg viewBox="0 0 24 24" fill="currentColor" width="18" height="18">
                                                    <path fill-rule="evenodd" d="M12 3.75a8.25 8.25 0 1 0 8.18 9.33l-.95.95a.75.75 0 1 1-1.06-1.06l2-2a.75.75 0 0 1 1.06 0l2 2a.75.75 0 0 1-1.06 1.06l-.53-.53a9.75 9.75 0 1 1-1.24-********** 0 0 1-1.3.76A8.25 8.25 0 0 0 12 3.75Zm0 2.5c.41 0 .75.34.75.75v4.6l2.67 1.78a.75.75 0 1 1-.84 1.24l-3-2a.75.75 0 0 1-.33-.62V7c0-.41.34-.75.75-.75Z" clip-rule="evenodd"></path>
                                                </svg>
                                                <span></span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <button type="submit" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-white border bg-primary-200 border-primary-200 hover:bg-trusted-700 search_submit_btn absolute bottom-0 h-11 w-full rounded-md !p-0 !px-0 lg:relative lg:flex lg:h-8 lg:w-8 lg:min-w-[32px] lg:cursor-pointer lg:items-center lg:justify-center lg:rounded-full">
                                    <svg viewBox="0 0 24 24" fill="currentColor" class="hidden lg:inline-block" width="16" height="16">
                                        <path fill-rule="evenodd" d="M11 3.75a7.25 7.25 0 1 0 0 14.5 7.25 7.25 0 0 0 0-14.5Zm6.7 12.88a8.75 8.75 0 1 0-1.06 1.06l3.83 3.84a.75.75 0 1 0 1.06-1.06l-3.84-3.84Z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="inline-block lg:hidden">Search</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class=" lg:hidden">
                    <div class="w-full min-w-[300px] cursor-pointer rounded-full border border-solid border-gray-300 bg-white px-3 py-[11px] shadow-md">
                        <div class="flex items-center gap-2">
                            <div class="iconsContainer">
                                <svg viewBox="0 0 24 24" fill="currentColor" width="18">
                                    <path fill-rule="evenodd" d="M11 3.75a7.25 7.25 0 1 0 0 14.5 7.25 7.25 0 0 0 0-14.5Zm6.7 12.88a8.75 8.75 0 1 0-1.06 1.06l3.83 3.84a.75.75 0 1 0 1.06-1.06l-3.84-3.84Z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="box-orient-vertical line-clamp-1 overflow-hidden opacity-70">Search by job, company or skills</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex_around h-9 min-w-max gap-6 md:border-trusted-300 ">
                    <div class="flex gap-6">
                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200">
                            <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M12 2C6.477 2 2 6.477 2 12C2 17.523 6.477 22 12 22C17.523 22 22 17.523 22 12C22 6.477 17.523 2 12 2ZM12 4C16.418 4 20 7.582 20 12C20 13.5973 19.5254 15.0811 18.7188 16.3301L17.9492 15.7344C16.3972 14.5374 13.537 14 12 14C10.463 14 7.60178 14.5374 6.05078 15.7344L5.28125 16.332C4.47404 15.0828 4 13.5979 4 12C4 7.582 7.582 4 12 4ZM12 5.75C10.208 5.75 8.75 7.208 8.75 9C8.75 10.792 10.208 12.25 12 12.25C13.792 12.25 15.25 10.792 15.25 9C15.25 7.208 13.792 5.75 12 5.75ZM12 7.75C12.689 7.75 13.25 8.311 13.25 9C13.25 9.689 12.689 10.25 12 10.25C11.311 10.25 10.75 9.689 10.75 9C10.75 8.311 11.311 7.75 12 7.75ZM12 16C15.1007 16 16.7681 17.1685 17.5488 17.7539C16.11 19.1418 14.1569 20 12 20C9.84315 20 7.89002 19.1418 6.45117 17.7539C7.2319 17.1685 8.89929 16 12 16ZM6.05469 17.3398C6.17566 17.4731 6.29727 17.6059 6.42578 17.7305C6.29711 17.6053 6.17473 17.4734 6.05469 17.3398ZM17.9121 17.375C17.8024 17.4955 17.6929 17.6168 17.5762 17.7305C17.6926 17.6175 17.8015 17.495 17.9121 17.375Z"></path>
                            </svg>
                            Login
                        </button>
                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-white border bg-primary-300 border-primary-300 active:bg-secondary-orange-1">
                            <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10 3.75C7.92897 3.75 6.25004 5.42893 6.25004 7.5C6.25004 9.57107 7.92897 11.25 10 11.25C12.0711 11.25 13.75 9.57107 13.75 7.5C13.75 5.42893 12.0711 3.75 10 3.75ZM4.75004 7.5C4.75004 4.60051 7.10055 2.25 10 2.25C12.8995 2.25 15.25 4.60051 15.25 7.5C15.25 10.3995 12.8995 12.75 10 12.75C7.10055 12.75 4.75004 10.3995 4.75004 7.5ZM18.2197 10.2197C19.203 9.23642 20.7971 9.2364 21.7804 10.2196C22.7637 11.2029 22.7637 12.7971 21.7804 13.7804L15.3235 20.2372C15.3165 20.2442 15.3094 20.2513 15.3022 20.2585C15.2147 20.3462 15.1209 20.4402 15.0118 20.5203C14.9163 20.5904 14.814 20.6506 14.7064 20.7C14.5833 20.7565 14.4556 20.7929 14.3364 20.8268C14.3267 20.8296 14.317 20.8323 14.3074 20.835L11.2061 21.7211C10.9442 21.796 10.6623 21.7229 10.4697 21.5303C10.2771 21.3377 10.2041 21.0559 10.2789 20.794L11.165 17.6926C11.1677 17.683 11.1705 17.6733 11.1733 17.6636C11.2072 17.5444 11.2435 17.4167 11.3 17.2937C11.3494 17.186 11.4096 17.0837 11.4797 16.9883C11.5599 16.8791 11.6538 16.7853 11.7416 16.6978C11.7487 16.6907 11.7558 16.6836 11.7629 16.6765L18.2197 10.2197ZM20.7198 11.2803C20.3223 10.8829 19.6779 10.8829 19.2804 11.2803L12.8235 17.7372C12.7659 17.7948 12.7342 17.8266 12.7107 17.8515C12.6904 17.8731 12.6874 17.8779 12.6889 17.8759C12.6788 17.8896 12.6702 17.9042 12.6632 17.9196C12.6642 17.9173 12.6615 17.9223 12.6525 17.9505C12.6421 17.9831 12.6297 18.0264 12.6073 18.1047L12.0921 19.908L13.8954 19.3928C13.9737 19.3704 14.0169 19.3579 14.0495 19.3476C14.0777 19.3386 14.0827 19.3358 14.0805 19.3369C14.0959 19.3298 14.1105 19.3212 14.1241 19.3112C14.1221 19.3127 14.127 19.3096 14.1485 19.2893C14.1734 19.2659 14.2053 19.2341 14.2629 19.1765L20.7198 12.7197C21.1172 12.3222 21.1172 11.6778 20.7198 11.2803ZM7.35703 14.75C7.40406 14.75 7.45173 14.75 7.50005 14.75H9.00004C9.41426 14.75 9.75004 15.0858 9.75004 15.5C9.75004 15.9142 9.41426 16.25 9.00004 16.25H7.50005C6.04991 16.25 5.49111 16.2581 5.05661 16.3899C4.01791 16.705 3.20507 17.5179 2.88999 18.5566C2.75818 18.9911 2.75004 19.5499 2.75004 21C2.75004 21.4142 2.41425 21.75 2.00004 21.75C1.58583 21.75 1.25004 21.4142 1.25004 21C1.25004 20.9517 1.25003 20.904 1.25002 20.857C1.24974 19.5989 1.24956 18.797 1.45458 18.1211C1.91509 16.603 3.10308 15.415 4.62119 14.9545C5.29702 14.7495 6.09892 14.7497 7.35703 14.75Z"></path>
                            </svg>
                            Register
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <section>
            <div id="jdPage" class="w-full bg-surface-primary-normal pb-28 md:bg-surface-background md:pt-4">
                <div class="flex flex-col gap-6 md:m-auto md:max-w-[1203px] md:!flex-row lg:!flex-row">
                    <div class="flex w-full max-w-[796px] flex-col gap-6">
                        <div class="jdSection flex flex-col gap-4 md:!gap-6">
                            <div id="jobDetailContainer" class="bg-surface-primary-normal md:!rounded-2xl md:!border md:!border-solid md:!border-border-subtle">
                                <section>
                                    <script type="application/ld+json">
                                        {
                                            "@context": "https://schema.org",
                                            "@type": "JobPosting",
                                            "title": "Python Developer",
                                            "description": "<ul><li>Proven experience as a Data Engineer.</li><li>Strong expertise in Python and SQL.</li><li>Experience with ETL techniques and dealing with different data types and formats.</li><li>Strong knowledge of different database systems, both RDBMS SQL and NoSQL. Hive/HBase is an advantage</li><li>Strong knowledge and experience in using Cloud (AWS, Azure, GCP)</li><li>Experience with SDLC (Software Development Life Cycle) such as Agile Scrum, Kanban, Jira</li><li>Familiarity with data processing tools and frameworks (e.g., Airflow, Hadoop, Spark).</li><li>Experience with Azure DevOps, Dockers, and Kubernetes is desired.</li></ul><p><strong>Required Skills</strong> AWS;Azure DevOps;Jira;Python;SQL</p>",
                                            "identifier": {
                                                "@type": "PropertyValue",
                                                "name": "Heptarc Technology Solutions Private Limited",
                                                "value": ********
                                            },
                                            "datePosted": "09-04-2025",
                                            "validThrough": "21-09-2025",
                                            "url": "www.foundit.in/job/python-developer-heptarc-technology-solutions-private-limited-hyderabad-secunderabad-telangana-********",
                                            "directApply": true,
                                            "employmentType": "Full time",
                                            "jobLocation": {
                                                "@type": "Place",
                                                "address": [
                                                    {
                                                        "@type": "PostalAddress",
                                                        "addressLocality": "Hyderabad / Secunderabad, Telangana",
                                                        "addressRegion": "Telangana",
                                                        "addressCountry": 91,
                                                        "postalCode": "NA",
                                                        "streetAddress": "NA"
                                                    },
                                                    {
                                                        "@type": "PostalAddress",
                                                        "addressLocality": "Bengaluru / Bangalore",
                                                        "addressRegion": "Karnataka",
                                                        "addressCountry": 91,
                                                        "postalCode": "NA",
                                                        "streetAddress": "NA"
                                                    }
                                                ]
                                            },
                                            "jobLocationType": "",
                                            "hiringOrganization": {
                                                "name": "Heptarc Technology Solutions Private Limited",
                                                "sameAs": "Heptarc Technology Solutions Private Limited",
                                                "@type": "Organization"
                                            },
                                            "experienceRequirements": {
                                                "@type": "OccupationalExperienceRequirements",
                                                "monthsOfExperience": 60
                                            },
                                            "educationRequirements": "",
                                            "qualifications": "NA",
                                            "occupationalCategory": "IT",
                                            "responsibilities": [
                                                "Database Administrator (DBA)",
                                                "Datawarehousing Consultants"
                                            ],
                                            "industry": [
                                                "Software"
                                            ],
                                            "skills": [
                                                "",
                                                "AWS",
                                                "Data Engineer",
                                                "Sdlc Concepts",
                                                "Azure Devops"
                                            ],
                                            "baseSalary": {
                                                "@type": "MonetaryAmount",
                                                "currency": "INR",
                                                "value": {
                                                    "@type": "QuantitativeValue",
                                                    "minValue": "Salary Confidential",
                                                    "maxValue": "Salary Confidential",
                                                    "unitText": "YEAR"
                                                }
                                            }
                                        }</script>
                                    <script type="application/ld+json">
                                        {
                                            "@context": "https://schema.org",
                                            "@type": "BreadcrumbList",
                                            "itemListElement": [
                                                {
                                                    "@type": "ListItem",
                                                    "position": 1,
                                                    "item": {
                                                        "@id": "https://www.foundit.in",
                                                        "name": "Home"
                                                    }
                                                },
                                                {
                                                    "@type": "ListItem",
                                                    "position": 2,
                                                    "item": {
                                                        "@id": "https://www.foundit.in/search/jobs-in-hyderabad-secunderabad-telangana",
                                                        "name": "Jobs in Hyderabad / Secunderabad, Telangana"
                                                    }
                                                },
                                                {
                                                    "@type": "ListItem",
                                                    "position": 3,
                                                    "item": {
                                                        "name": "Python Developer"
                                                    }
                                                }
                                            ]
                                        }</script>
                                </section>
                                <div class="relative flex min-h-[94px] bg-jdPage-gradient px-6 py-4 md:!rounded-t-xl">
                                    <div class="absolute left-4 top-[45px] flex size-[70px] items-center justify-center rounded bg-surface-primary-normal p-px md:!left-[30px] md:!top-[50px]">
                                        <img src="https://media.monsterindia.com/logos/xeft_heptarcinx/jdlogo.gif" alt="Heptarc Technology Solutions Private Limited" width="56" height="56"/>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-2 p-4 md:!mt-2 md:!p-6">
                                    <div class="mt-4 flex items-center justify-between md:!mt-0">
                                        <h1 class="text-content-primary line-clamp-2 text-lg font-bold leading-[26px] md:!text-2xl md:!leading-9">Python Developer</h1>
                                        <span class="cursor-pointer">
                                            <svg viewBox="0 0 24 24" width="24" height="24" fill="">
                                                <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <div class="text-content-secondary md:!text-content-primary -mt-1 line-clamp-2 text-base font-normal leading-6 md:!text-lg md:!leading-[26px]">
                                        <a href="/search/heptarc-technology-solutions-private-limited-1059144-jobs-career" target="_blank" class="line-clamp-2 text-sm font-normal underline md:!text-base">Heptarc Technology Solutions Private Limited</a>
                                    </div>
                                    <div class="flex flex-col gap-2">
                                        <div class="flex gap-2">
                                            <div class="bg-decorative-surface-06 flex items-center gap-[6px] rounded px-2 py-1">
                                                <svg viewBox="0 0 16 16" height="16" width="16" fill="#2BA682">
                                                    <g clip-path="url(#clip0_944_32456)">
                                                        <path d="M15.8047 10.8833C15.6797 10.7584 15.5102 10.6881 15.3334 10.6881C15.1566 10.6881 14.9871 10.7584 14.8621 10.8833L11.1107 14.6353L9.31872 12.8667C9.19495 12.7398 9.02586 12.6673 8.84864 12.6651C8.67141 12.6629 8.50058 12.7312 8.37372 12.855C8.24686 12.9788 8.17436 13.1479 8.17217 13.3251C8.16998 13.5023 8.23829 13.6731 8.36205 13.8L10.1914 15.6C10.3061 15.7237 10.4447 15.8229 10.5988 15.8917C10.7529 15.9604 10.9193 15.9973 11.0881 16H11.1101C11.2754 16.0005 11.4393 15.9682 11.592 15.9049C11.7448 15.8417 11.8835 15.7487 12.0001 15.6313L15.8047 11.826C15.9297 11.701 15.9999 11.5314 15.9999 11.3547C15.9999 11.1779 15.9297 11.0084 15.8047 10.8833Z"></path>
                                                        <path d="M7.39333 14.6393C6.10603 14.5217 4.88069 14.0324 3.86648 13.2309C2.85228 12.4294 2.09297 11.3503 1.68098 10.125C1.269 8.89979 1.22211 7.58118 1.54604 6.32975C1.86996 5.07833 2.55072 3.94807 3.50544 3.07657C4.46016 2.20507 5.64765 1.62993 6.92335 1.42117C8.19905 1.21241 9.50793 1.37903 10.6906 1.90074C11.8734 2.42246 12.8789 3.27677 13.5848 4.35966C14.2907 5.44256 14.6666 6.70733 14.6667 8C14.6667 8.20466 14.6567 8.40733 14.6393 8.60733C14.6312 8.69474 14.6404 8.7829 14.6664 8.86675C14.6924 8.9506 14.7347 9.02848 14.7909 9.09594C14.847 9.1634 14.916 9.21909 14.9938 9.25983C15.0715 9.30057 15.1566 9.32555 15.244 9.33333C15.3314 9.34212 15.4196 9.33337 15.5036 9.30758C15.5875 9.2818 15.6655 9.2395 15.7329 9.18317C15.8003 9.12685 15.8557 9.05763 15.896 8.97958C15.9362 8.90153 15.9605 8.81622 15.9673 8.72866C15.9887 8.48666 16 8.24466 16 8C15.9999 6.44874 15.5488 4.93096 14.7017 3.63146C13.8545 2.33195 12.6478 1.30679 11.2285 0.680764C9.80912 0.054743 8.2384 -0.145123 6.70752 0.105499C5.17664 0.356121 3.75164 1.04642 2.60603 2.09235C1.46041 3.13828 0.643599 4.49473 0.255034 5.99653C-0.133531 7.49834 -0.0770852 9.08072 0.4175 10.551C0.912085 12.0213 1.82347 13.3161 3.0407 14.2778C4.25793 15.2394 5.72849 15.8264 7.27333 15.9673H7.334C7.51081 15.9752 7.68351 15.9125 7.81409 15.793C7.94468 15.6736 8.02247 15.5071 8.03033 15.3303C8.0382 15.1535 7.97551 14.9808 7.85605 14.8502C7.73659 14.7197 7.57015 14.6419 7.39333 14.634V14.6393Z"></path>
                                                        <path d="M7.33339 4.66667V7.724L5.52872 9.52867C5.46505 9.59016 5.41426 9.66373 5.37932 9.74506C5.34438 9.8264 5.32599 9.91388 5.32522 10.0024C5.32445 10.0909 5.34132 10.1787 5.37484 10.2606C5.40836 10.3426 5.45786 10.417 5.52046 10.4796C5.58305 10.5422 5.65749 10.5917 5.73942 10.6252C5.82135 10.6587 5.90914 10.6756 5.99765 10.6748C6.08617 10.6741 6.17365 10.6557 6.25499 10.6207C6.33633 10.5858 6.40989 10.535 6.47139 10.4713L8.47139 8.47133C8.59642 8.34634 8.66668 8.1768 8.66672 8V4.66667C8.66672 4.48986 8.59648 4.32029 8.47146 4.19526C8.34643 4.07024 8.17687 4 8.00005 4C7.82324 4 7.65367 4.07024 7.52865 4.19526C7.40363 4.32029 7.33339 4.48986 7.33339 4.66667Z"></path>
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_944_32456">
                                                            <rect width="16" height="16" fill="white"></rect>
                                                        </clipPath>
                                                    </defs>
                                                </svg>
                                                <span class="w-max text-xs font-normal">Early Applicant</span>
                                            </div>
                                            <div class="bg-decorative-surface-06 flex items-center gap-[6px] rounded px-2 py-1">
                                                <svg viewBox="0 0 16 16" height="16" width="16" fill="#2BA682">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.86583 0.87469C9.06944 0.963075 9.19041 1.17511 9.16288 1.39536L8.56646 6.16667L12.8813 6.16667C13.0456 6.16666 13.2014 6.16664 13.3266 6.17797C13.4464 6.18882 13.6441 6.21604 13.8122 6.35185C14.0114 6.51283 14.1255 6.75647 14.1216 7.01257C14.1184 7.22863 14.0127 7.3979 13.9443 7.49694C13.8729 7.60034 13.7732 7.71997 13.668 7.84619L7.71751 14.9868C7.57542 15.1573 7.33791 15.2137 7.13431 15.1253C6.9307 15.0369 6.80973 14.8249 6.83727 14.6047L7.43368 9.83334L3.11881 9.83334C2.95448 9.83336 2.79874 9.83338 2.67358 9.82205C2.55371 9.81119 2.35604 9.78398 2.18798 9.64816C1.98877 9.48718 1.87465 9.24354 1.87851 8.98744C1.88177 8.77139 1.9874 8.60211 2.0558 8.50308C2.12723 8.39966 2.22695 8.28003 2.33217 8.15379L8.28263 1.01325C8.42472 0.842732 8.66223 0.786306 8.86583 0.87469ZM7.95907 2.96357L3.11315 8.77867C3.0971 8.79793 3.08196 8.81611 3.06767 8.8333C3.09003 8.83333 3.11369 8.83334 3.13876 8.83334H8.00007C8.14349 8.83334 8.28 8.89492 8.37491 9.00244C8.46982 9.10995 8.514 9.25305 8.49621 9.39536L8.04107 13.0364L12.887 7.22135C12.903 7.20209 12.9182 7.1839 12.9325 7.16671C12.9101 7.16668 12.8865 7.16667 12.8614 7.16667H8.00007C7.85666 7.16667 7.72014 7.10509 7.62523 6.99758C7.53032 6.89006 7.48614 6.74697 7.50393 6.60466L7.95907 2.96357Z"></path>
                                                </svg>
                                                <span class="w-max text-xs font-normal">Quick Apply</span>
                                            </div>
                                        </div>
                                        <div class="text-content-tertiary text-sm font-normal md:!max-h-5">
                                            <ul class="flex gap-4">
                                                <li class="">
                                                    <span class="ml-[-4px]">Posted 3 hours ago</span>
                                                </li>
                                                <li class="">
                                                    <span class="ml-[-4px]">Be among the first 20 applicants</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-3 px-4 md:!gap-4 md:!px-6">
                                    <div class="text-content-primary flex gap-4 text-sm font-normal md:!max-h-5">
                                        <div class="flex items-center gap-2">
                                            <span class="mt-[2px] self-start">
                                                <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                    <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                </svg>
                                            </span>
                                            <span>5-7 Years</span>
                                        </div>
                                    </div>
                                    <div class="text-content-primary flex gap-4 text-sm font-normal">
                                        <div class="flex items-center gap-2">
                                            <span class="mt-[2px] self-start">
                                                <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                    <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                </svg>
                                            </span>
                                            <a href="/search/jobs-in-hyderabad" title="Jobs in Hyderabad">Hyderabad
                                            <!-- -->
                                            , </a>
                                            <a href="/search/jobs-in-bengaluru" title="Jobs in Bengaluru">Bengaluru</a>
                                        </div>
                                    </div>
                                    <div class="text-content-primary flex gap-4 text-sm font-normal">
                                        <div class="flex items-center gap-2">
                                            <span class="mt-[2px] self-start">
                                                <svg viewBox="0 0 15 14" width="16" height="16" fill="#59566C">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M3.61321 0.5H6.05379C6.40989 0.499993 6.70712 0.499987 6.94994 0.519827C7.2032 0.540519 7.44097 0.585259 7.66581 0.699822C8.01078 0.87559 8.29124 1.15605 8.46701 1.50102C8.58157 1.72586 8.62631 1.96363 8.647 2.21689C8.66684 2.45971 8.66684 2.75695 8.66683 3.11306V5.83333H11.3871C11.7432 5.83333 12.0405 5.83332 12.2833 5.85316C12.5365 5.87385 12.7743 5.91859 12.9991 6.03316C13.3441 6.20892 13.6246 6.48939 13.8003 6.83435C13.9149 7.05919 13.9596 7.29696 13.9803 7.55022C14.0002 7.79304 14.0002 8.09027 14.0002 8.44637V12.5H14.1668C14.443 12.5 14.6668 12.7239 14.6668 13C14.6668 13.2761 14.443 13.5 14.1668 13.5H0.833496C0.557354 13.5 0.333496 13.2761 0.333496 13C0.333496 12.7239 0.557354 12.5 0.833496 12.5H1.00016L1.00016 3.11304C1.00016 2.75694 1.00015 2.45971 1.01999 2.21689C1.04068 1.96363 1.08542 1.72586 1.19998 1.50102C1.37575 1.15605 1.65622 0.87559 2.00118 0.699822C2.22602 0.585259 2.46379 0.540519 2.71705 0.519827C2.95987 0.499987 3.2571 0.499993 3.61321 0.5ZM2.00016 12.5H7.66683V3.13333C7.66683 2.75172 7.66644 2.49557 7.65032 2.29832C7.63468 2.1069 7.60676 2.01538 7.576 1.95501C7.49611 1.79821 7.36862 1.67072 7.21182 1.59083C7.15145 1.56007 7.05993 1.53214 6.86851 1.51651C6.67126 1.50039 6.41511 1.5 6.0335 1.5H3.6335C3.25188 1.5 2.99573 1.50039 2.79848 1.51651C2.60707 1.53214 2.51554 1.56007 2.45517 1.59083C2.29837 1.67072 2.17089 1.79821 2.09099 1.95501C2.06023 2.01538 2.03231 2.1069 2.01667 2.29832C2.00055 2.49557 2.00016 2.75172 2.00016 3.13333V12.5ZM8.66683 6.83333V12.5H13.0002V8.46667C13.0002 8.08505 12.9998 7.82891 12.9837 7.63165C12.968 7.44024 12.9401 7.34872 12.9093 7.28834C12.8294 7.13154 12.702 7.00406 12.5452 6.92416C12.4848 6.8934 12.3933 6.86548 12.2018 6.84984C12.0046 6.83372 11.7484 6.83333 11.3668 6.83333H8.66683ZM3.3335 3.66667C3.3335 3.39052 3.55735 3.16667 3.8335 3.16667H5.8335C6.10964 3.16667 6.3335 3.39052 6.3335 3.66667C6.3335 3.94281 6.10964 4.16667 5.8335 4.16667H3.8335C3.55735 4.16667 3.3335 3.94281 3.3335 3.66667ZM3.3335 6.33333C3.3335 6.05719 3.55735 5.83333 3.8335 5.83333H5.8335C6.10964 5.83333 6.3335 6.05719 6.3335 6.33333C6.3335 6.60948 6.10964 6.83333 5.8335 6.83333H3.8335C3.55735 6.83333 3.3335 6.60948 3.3335 6.33333ZM3.3335 9C3.3335 8.72386 3.55735 8.5 3.8335 8.5H5.8335C6.10964 8.5 6.3335 8.72386 6.3335 9C6.3335 9.27614 6.10964 9.5 5.8335 9.5H3.8335C3.55735 9.5 3.3335 9.27614 3.3335 9Z"></path>
                                                </svg>
                                            </span>
                                            <div class="flex flex-wrap">
                                                <p class="text-content-primary mr-1 text-sm">Software</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="ml-6 mt-6 w-max" id="applyJobJD">
                                    <div class="">
                                        <a href="?autoApply=true" target="_blank" rel="nofollow" class="cursor-pointer">
                                            <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit undefined" id="applyBtn">
                                                <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                    <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                </svg>
                                                Quick Apply
                                            </button>
                                        </a>
                                    </div>
                                </div>
                                <div id="jobDescription" class="job-description-content mt-6 flex flex-col gap-6 px-4 pt-4 text-xs md:!mt-0 md:!px-6  md:!text-sm">
                                    <div>
                                        <h2 class="mb-3 text-base font-bold leading-6 md:!mb-4 md:!text-lg md:!leading-[26px]">Job Description</h2>
                                        <div class="break-words text-[13px] leading-6 md:!text-[14px] md:!leading-7">
                                            <ul>
                                                <li>Proven experience as a Data Engineer.</li>
                                                <li>Strong expertise in Python and SQL.</li>
                                                <li>Experience with ETL techniques and dealing with different data types and formats.</li>
                                                <li>Strong knowledge of different database systems, both RDBMS SQL and NoSQL. Hive/HBase is an advantage</li>
                                                <li>Strong knowledge and experience in using Cloud (AWS, Azure, GCP)</li>
                                                <li>Experience with SDLC (Software Development Life Cycle) such as Agile Scrum, Kanban, Jira</li>
                                                <li>Familiarity with data processing tools and frameworks (e.g., Airflow, Hadoop, Spark).</li>
                                                <li>Experience with Azure DevOps, Dockers, and Kubernetes is desired.</li>
                                            </ul>
                                            <p>
                                                <strong>Required Skills</strong>
                                                AWS;Azure DevOps;Jira;Python;SQL
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-4 flex flex-col gap-3 py-4 pl-4 md:!mt-0 md:!py-6 md:!pl-6">
                                    <h2 class="text-content-primary text-base font-bold leading-6 md:!text-lg md:!leading-[26px]">More Info</h2>
                                    <div class="flex flex-col gap-1">
                                        <p class="flex gap-1 text-sm">
                                            <span class="text-content-primary font-medium">Role:</span>
                                            <span class="text-content-secondary font-normal">
                                                <a href="/search/database-administrator-dba-jobs" class="text-content-primary cursor-pointer text-xs md:!text-sm">Database Administrator (DBA)
                                                <!-- -->
                                                , </a>
                                                <a href="/search/datawarehousing-consultants-jobs" class="text-content-primary cursor-pointer text-xs md:!text-sm">Datawarehousing Consultants</a>
                                            </span>
                                        </p>
                                        <p class="flex gap-1 text-sm">
                                            <span class="text-content-primary font-medium">Industry:</span>
                                            <span class="text-content-secondary font-normal">
                                                <a href="/search/software-jobs" class="text-content-primary cursor-pointer text-xs md:!text-sm">Software</a>
                                            </span>
                                        </p>
                                        <p class="flex gap-1 text-sm">
                                            <span class="text-content-primary font-medium">Function:</span>
                                            <span class="text-content-secondary font-normal">
                                                <a href="/search/it-jobs" class="text-content-primary cursor-pointer text-xs md:!text-sm">IT</a>
                                            </span>
                                        </p>
                                        <p class="flex gap-1 text-sm">
                                            <span class="text-content-primary font-medium">Job Type:</span>
                                            <span class="text-content-secondary font-normal">
                                                <a href="/search/permanent-jobs" class="text-content-primary cursor-pointer text-xs md:!text-sm">Permanent Job</a>
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex h-11 items-center justify-between border-t border-solid border-surface-tertiary py-6 pl-4 pr-6 md:!px-6 md:!py-8">
                                    <div class="flex flex-col gap-1">
                                        <p class="text-content-tertiary text-xs leading-4">Date Posted: 
                                        <!-- -->
                                        09/04/2025</p>
                                        <p class="text-content-tertiary text-xs leading-4">Job ID: 
                                        <!-- -->
                                        *********</p>
                                    </div>
                                    <div class="text-brand-primary flex cursor-pointer items-center justify-center gap-2">
                                        <svg viewBox="0 0 16 16" fill="#6E00BE" height="16" width="16">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.06426 1.20247C3.54637 1.00963 4.27693 0.833374 5.33329 0.833374C6.42992 0.833374 7.33978 1.19739 8.15658 1.52417L8.18565 1.5358C9.02804 1.87275 9.77628 2.16671 10.6666 2.16671C11.6103 2.16671 12.213 2.00963 12.5643 1.86914C12.7403 1.79871 12.8555 1.73156 12.9205 1.68818C12.9532 1.66643 12.9735 1.65045 12.9829 1.64272C12.9853 1.64074 12.987 1.6393 12.988 1.63843C13.1309 1.5019 13.3414 1.46218 13.5246 1.5381C13.7115 1.61549 13.8333 1.79781 13.8333 2.00004V10C13.8333 10.1326 13.7806 10.2598 13.6868 10.3536L13.3333 10C13.6868 10.3536 13.6866 10.3539 13.6863 10.3541L13.6858 10.3547L13.6846 10.3558L13.6822 10.3582L13.6764 10.3638L13.6617 10.3775C13.6503 10.3878 13.636 10.4003 13.6186 10.4147C13.5837 10.4434 13.5363 10.4795 13.4752 10.5202C13.3528 10.6018 13.1763 10.7014 12.9357 10.7976C12.4535 10.9905 11.723 11.1667 10.6666 11.1667C9.57 11.1667 8.66014 10.8027 7.84334 10.4759L7.81426 10.4643C6.97188 10.1273 6.22364 9.83337 5.33329 9.83337C4.38965 9.83337 3.78688 9.99045 3.43565 10.1309C3.32124 10.1767 3.23256 10.2211 3.16663 10.2585L3.16663 14.6667C3.16663 14.9428 2.94277 15.1667 2.66663 15.1667C2.39048 15.1667 2.16663 14.9428 2.16663 14.6667L2.16663 10.0006C2.16663 10.0002 2.16663 9.99984 2.16663 9.99943L2.16663 2.00004C2.16663 1.86743 2.2193 1.74026 2.31307 1.64649L2.66663 2.00004C2.31307 1.64649 2.31333 1.64623 2.3136 1.64596L2.31414 1.64542L2.31528 1.6443L2.31775 1.64187L2.32349 1.63632L2.33826 1.62258C2.34959 1.61229 2.36389 1.59979 2.38132 1.58543C2.41621 1.5567 2.46357 1.5206 2.52469 1.47985C2.64712 1.39823 2.82365 1.29871 3.06426 1.20247ZM3.16663 2.25847L3.16663 9.16321C3.64776 8.98636 4.34914 8.83337 5.33329 8.83337C6.42992 8.83337 7.33978 9.19739 8.15658 9.52417L8.18565 9.5358C9.02804 9.87275 9.77628 10.1667 10.6666 10.1667C11.6103 10.1667 12.213 10.0096 12.5643 9.86913C12.6787 9.82337 12.7674 9.77899 12.8333 9.74161V2.83687C12.3522 3.01372 11.6508 3.16671 10.6666 3.16671C9.57 3.16671 8.66014 2.80269 7.84334 2.47591L7.81426 2.46428C6.97188 2.12733 6.22364 1.83337 5.33329 1.83337C4.38965 1.83337 3.78688 1.99045 3.43565 2.13095C3.32124 2.17671 3.23256 2.22109 3.16663 2.25847Z"></path>
                                        </svg>
                                        <span class="text-sm font-semibold">Report Job</span>
                                    </div>
                                </div>
                                <div id="jobCompany" class="border-border-subtle border-t border-solid p-4 md:!p-6">
                                    <div class="flex flex-col gap-3">
                                        <h2 class="text-content-primary text-lg font-bold leading-[26px]">About Company</h2>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2 md:!size-11 md:!rounded md:!border md:!border-solid md:!border-surface-tertiary">
                                                <img src="https://media.monsterindia.com/logos/xeft_heptarcinx/jdlogo.gif" alt="Heptarc Technology Solutions Private Limited" width="44" height="44"/>
                                                <div class="flex flex-col gap-2">
                                                    <label class="line-clamp-2 text-sm font-normal md:!w-max md:!text-base">
                                                        <a href="/search/heptarc-technology-solutions-private-limited-1059144-jobs-career" target="_blank" class="!text-content-primary text-base font-medium leading-6 hover:underline" style="color:#17142A !important">Heptarc Technology Solutions Private Limited</a>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="w-328px mx-4 flex items-center justify-between rounded-lg bg-decorative-surface-02 px-3 py-2 md:!mx-0 md:!p-3">
                                <div class="flex items-center gap-2">
                                    <svg viewBox="0 0 32 32" height="32" width="32">
                                        <path d="M6.10477 28.3101C5.28382 28.3101 4.61523 27.6439 4.61523 26.8259V4.68416C4.61523 3.86615 5.28382 3.19995 6.10477 3.19995H24.0455C24.8665 3.19995 25.5351 3.86615 25.5351 4.68416V9.98696C25.5351 10.2084 25.354 10.3888 25.1318 10.3888C24.9095 10.3888 24.7285 10.2084 24.7285 9.98696V4.68416C24.7285 4.30909 24.4219 4.00368 24.0455 4.00368H6.10477C5.72835 4.00368 5.42184 4.30909 5.42184 4.68416V26.8259C5.42184 27.201 5.72835 27.5064 6.10477 27.5064H24.0455C24.4219 27.5064 24.7285 27.201 24.7285 26.8259V23.2413C24.7285 23.0198 24.9095 22.8394 25.1318 22.8394C25.354 22.8394 25.5351 23.0198 25.5351 23.2413V26.8259C25.5351 27.6439 24.8665 28.3101 24.0455 28.3101H6.10477Z" fill="#E119A0"></path>
                                        <path d="M19.7284 25.6863C19.6442 25.6863 19.5707 25.6524 19.5151 25.5898C19.4398 25.5059 19.404 25.3737 19.4165 25.2291C19.456 24.7968 19.5061 24.3503 19.551 23.9556L19.6477 23.0804C19.7033 22.5678 19.7589 22.0499 19.827 21.5444C19.8467 21.4015 19.8897 21.1944 19.9901 21.0193C21.8095 17.8652 23.8045 14.4181 26.0899 10.4816C26.495 9.78325 27.1223 9.39746 27.8572 9.39746C28.1602 9.39746 28.4702 9.46355 28.7803 9.59571C30.243 10.219 29.9024 11.5979 29.8863 11.655C29.6318 12.4355 29.1926 13.1482 28.7696 13.8376C28.6101 14.0966 28.4452 14.3627 28.2928 14.6306C26.8319 17.1864 25.3263 19.7727 23.8672 22.2731L23.4675 22.959C23.3707 23.1269 23.2291 23.2858 23.0678 23.4037C22.1142 24.1181 21.1409 24.8343 20.1694 25.5291C20.0403 25.622 19.87 25.6827 19.7374 25.6827H19.7266L19.7284 25.6863ZM20.3181 24.4235L22.2594 22.9983L20.5852 22.0356L20.3181 24.4235ZM20.8057 21.2211L22.8563 22.3999L27.9648 13.5822L25.916 12.4034L20.8057 21.2211ZM27.8805 10.1887C27.4826 10.1887 27.1116 10.3959 26.8839 10.7441C26.7602 10.9335 26.6509 11.1264 26.5344 11.3318L26.3229 11.6997L28.3734 12.8767L28.5868 12.5106C28.7068 12.3052 28.8216 12.1105 28.9237 11.9087C29.2177 11.33 29.0313 10.6745 28.481 10.353C28.2964 10.2441 28.0884 10.1869 27.8805 10.1869V10.1887Z" fill="#00A3FF"></path>
                                        <path d="M15.042 14.8093C12.5738 14.7968 10.5698 12.7875 10.5752 10.3317C10.5806 7.87408 12.5917 5.87549 15.0564 5.87549C17.5282 5.87906 19.5393 7.88301 19.5411 10.3406C19.5411 11.5284 19.0733 12.6482 18.2236 13.4948C17.374 14.3414 16.2519 14.8076 15.0635 14.8076H15.0402L15.042 14.8093ZM15.0886 11.8409C15.0582 11.8409 15.0295 11.8409 15.0008 11.8409C14.0938 11.8731 13.384 12.2749 12.8893 13.0322L12.7889 13.1876L12.9377 13.2983C13.5417 13.7448 14.2981 13.9913 15.0671 13.9913C15.8361 13.9913 16.5997 13.7448 17.1768 13.2965L17.3113 13.1912L17.227 13.0429C16.8094 12.3106 15.9705 11.8391 15.0922 11.8391L15.0886 11.8409ZM15.0635 8.13485C15.8934 8.13485 16.6086 8.74747 16.7287 9.56012C16.8201 10.1852 16.7377 10.671 16.4688 11.0872L16.3505 11.2712L16.5459 11.3694C17.0406 11.6177 17.4385 11.9463 17.7666 12.3749L17.9279 12.5875L18.0838 12.3714C19.24 10.7657 18.6987 8.69746 17.5067 7.62404C16.8381 7.02214 15.9723 6.68993 15.0671 6.68993C14.092 6.68993 13.1725 7.06679 12.477 7.75263C11.0932 9.11897 11.1273 11.1586 11.9518 12.266L12.0719 12.425L12.3623 12.2035C12.5308 12.0731 12.7064 11.9391 12.8857 11.8123C13.0614 11.6891 13.237 11.573 13.4234 11.4498L13.7461 11.2372L13.6403 11.0729C13.3732 10.6621 13.2944 10.1781 13.3912 9.5494C13.5166 8.73496 14.2139 8.13842 15.051 8.13306H15.0617L15.0635 8.13485ZM15.0384 8.94572C14.5222 8.94572 14.1745 9.3583 14.1727 9.9727C14.1727 10.5032 14.3645 10.8282 14.757 10.9675C14.8646 11.0068 14.9721 11.0265 15.0761 11.0265C15.2948 11.0265 15.5009 10.9372 15.673 10.7675C16.0279 10.4139 15.9831 9.96556 15.8934 9.57262C15.8164 9.24042 15.5744 9.02252 15.2123 8.96179C15.1532 8.95108 15.094 8.9475 15.0384 8.9475V8.94572Z" fill="#00A3FF"></path>
                                        <path d="M16.9276 17.3849H10.7866C10.749 17.3849 10.7113 17.3849 10.6737 17.3813C10.4407 17.3546 10.2811 17.1956 10.2794 16.9831C10.2758 16.7777 10.4389 16.6062 10.6647 16.5776C10.7024 16.5723 10.7382 16.5723 10.7759 16.5723H17.6392C17.9529 16.5776 18.1429 16.7277 18.1465 16.9741C18.15 17.2706 17.8919 17.3796 17.6464 17.3831C17.5567 17.3831 17.4689 17.3831 17.3793 17.3831H16.9276V17.3849Z" fill="#E119A0"></path>
                                        <path d="M10.7921 20.1585C10.7473 20.1585 10.6971 20.1585 10.6487 20.1514C10.4354 20.1192 10.2812 19.9513 10.2812 19.7531C10.2812 19.5548 10.4372 19.3869 10.6505 19.3548C10.6989 19.3477 10.7491 19.3477 10.7939 19.3477H17.5784C17.7899 19.3477 18.1484 19.403 18.1484 19.7531C18.1484 20.1192 17.7612 20.1585 17.5945 20.1585H16.2842H10.7939H10.7921Z" fill="#E119A0"></path>
                                        <path d="M10.7919 22.9324C10.7471 22.9324 10.6951 22.9324 10.6467 22.9234C10.4334 22.8913 10.2793 22.7234 10.2793 22.5251C10.2793 22.3251 10.4352 22.159 10.6485 22.1269C10.6969 22.1197 10.7507 22.1179 10.7955 22.1179H17.5764C17.7879 22.1179 18.1464 22.1733 18.1446 22.5251C18.1446 22.8913 17.7574 22.9306 17.5907 22.9306H16.0439H10.7901L10.7919 22.9324Z" fill="#E119A0"></path>
                                        <path d="M15.6545 25.7079C15.4914 25.7079 15.3283 25.7079 15.1634 25.7079C14.9196 25.7079 14.6579 25.6008 14.6543 25.3061C14.6543 25.1989 14.6884 25.1078 14.7583 25.0364C14.8461 24.9471 14.9859 24.8971 15.1526 24.8971C15.5595 24.8971 15.9682 24.8953 16.3751 24.8953C16.782 24.8953 17.2157 24.8953 17.6352 24.8971C17.8789 24.8971 18.1406 25.0042 18.1442 25.2989C18.1442 25.4061 18.112 25.4954 18.0438 25.565C17.9524 25.6579 17.8054 25.7079 17.6208 25.7097C17.4613 25.7097 17.3018 25.7097 17.1422 25.7097H16.4037H15.6527L15.6545 25.7079Z" fill="#E119A0"></path>
                                        <path d="M8.56053 17.3812C8.35081 17.3812 8.15902 17.1901 8.15723 16.9811C8.15723 16.8811 8.20024 16.7793 8.27911 16.7007C8.35798 16.6221 8.45836 16.5775 8.55874 16.5757C8.77383 16.5757 8.95846 16.7596 8.95846 16.9758C8.95846 17.1901 8.77384 17.3794 8.56233 17.3812H8.55874H8.56053Z" fill="#E119A0"></path>
                                        <path d="M8.56058 20.1549C8.35445 20.1549 8.16266 19.9656 8.15728 19.7584C8.15549 19.6601 8.19851 19.5583 8.27379 19.478C8.35087 19.3976 8.45304 19.3512 8.55342 19.3494C8.76672 19.3494 8.95493 19.5298 8.95852 19.7441C8.9621 19.9584 8.77926 20.1495 8.56775 20.1549H8.56058Z" fill="#E119A0"></path>
                                        <path d="M8.56053 22.9288C8.34902 22.9288 8.15723 22.7377 8.15723 22.5269C8.15723 22.4269 8.20204 22.3251 8.27911 22.2465C8.35619 22.1679 8.45836 22.1233 8.56053 22.1233C8.77563 22.1233 8.95846 22.3073 8.95846 22.5252C8.95846 22.7395 8.77204 22.927 8.56053 22.9288Z" fill="#E119A0"></path>
                                    </svg>
                                    <p class="text-sm font-medium leading-5 text-content-primary">Hi 
                                    <!-- -->
                                    , want to stand out? Get your resume crafted by experts.</p>
                                </div>
                                <a href="/career-services/resume-services">
                                    <svg viewBox="0 0 24 24" fill="currentColor" height="16" width="16">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.46967 5.46967C9.76256 5.17678 10.2374 5.17678 10.5303 5.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L10.5303 18.5303C10.2374 18.8232 9.76256 18.8232 9.46967 18.5303C9.17678 18.2374 9.17678 17.7626 9.46967 17.4697L14.9393 12L9.46967 6.53033C9.17678 6.23744 9.17678 5.76256 9.46967 5.46967Z"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                        <!--$?-->
                        <template id="B:0"></template>
                        <!--/$-->
                        <!--$-->
                        <!--/$-->
                        <div>
                            <!--$-->
                            <div class=" bg-surface-primary-normal p-4 !pb-2 text-xs  font-normal text-content-tertiary">Last Updated: 24-07-2025 09:23:30 AM</div>
                            <!--/$-->
                            <!--$-->
                            <div class="flex items-center justify-start gap-1 bg-surface-primary-normal px-4 text-content-tertiary">
                                <span class=" cursor-pointer text-xs font-normal text-brand-primary">
                                    <a href="/" target="_self" style="color:#6e00be !important">Home</a>
                                </span>
                                <span class="h-4 w-px rotate-[16deg] bg-content-tertiary text-base font-normal"></span>
                                <a href="/search/jobs-in-hyderabad-secunderabad-telangana" target="_self" class="cursor-pointer whitespace-nowrap text-xs font-normal text-brand-primary" style="color:#6e00be !important">Jobs in Hyderabad / Secunderabad, Telangana  </a>
                                <span class="h-4 w-px rotate-[16deg] bg-content-tertiary text-base font-normal"></span>
                                <span class="truncate text-xs font-normal capitalize text-content-tertiary">Python Developer</span>
                            </div>
                            <!--/$-->
                            <!--$-->
                            <!--/$-->
                        </div>
                        <!--$-->
                        <div class="rounded-2xl border bg-surface-primary-normal p-0 md:bg-surface-primary-normal md:!p-2">
                            <div class="accordion">
                                <div class="rotate-360 flex cursor-pointer justify-between border-subtle border-b p-4">
                                    <span class="text-sm font-normal text-content-primary">Jobs by Skill - IT</span>
                                    <svg fill="currentColor" viewBox="0 0 24 24" class="transition-transform duration-300 " width="16">
                                        <path fill-rule="evenodd" d="M5.47 9.47c.3-.3.77-.3 1.06 0L12 14.94l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6 6c-.3.3-.77.3-1.06 0l-6-6a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="transition-max-height max-h-0 overflow-hidden duration-300 ">
                                    <div class="flex flex-wrap gap-1 border-b border-border-subtle  bg-surface-primary-normal px-3 pb-2 pt-3 text-content-primary">
                                        <a href="/search/biotechnology-jobs?query=biotechnology " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Biotechnology Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/digital-marketing-jobs?query=digital marketing " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Digital Marketing Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/graphic-design-jobs?query=graphic design " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Graphic Design Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/networking-jobs?query=networking " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Networking Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/oracle-jobs?query=oracle " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Oracle Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/seo-jobs?query=seo " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">SEO Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/software-testing-jobs?query=software testing " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Software Testing Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/sql-jobs?query=sql " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Sql Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/web-design-jobs?query=web design " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Web Design Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/php-jobs?query=php " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">PHP Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion">
                                <div class="rotate-360 flex cursor-pointer justify-between border-subtle border-b p-4">
                                    <span class="text-sm font-normal text-content-primary">Jobs by Skill - Non IT</span>
                                    <svg fill="currentColor" viewBox="0 0 24 24" class="transition-transform duration-300 " width="16">
                                        <path fill-rule="evenodd" d="M5.47 9.47c.3-.3.77-.3 1.06 0L12 14.94l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6 6c-.3.3-.77.3-1.06 0l-6-6a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="transition-max-height max-h-0 overflow-hidden duration-300 ">
                                    <div class="flex flex-wrap gap-1 border-b border-border-subtle  bg-surface-primary-normal px-3 pb-2 pt-3 text-content-primary">
                                        <a href="/search/accounting-jobs?query=accounting " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Accounting Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/bpo-jobs?query=bpo " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">BPO Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/call-center-jobs?query=call center " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Call Center Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/civil-engineering-jobs?query=civil engineering " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Civil Engineering Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/content-writing-jobs?query=content writing " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Content Writing Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/electrical-engineering-jobs?query=electrical engineering " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Electrical Engineering Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/event-management-jobs?query=event management " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Event Management Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/hotel-management-jobs?query=hotel management " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Hotel Management Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/hr-jobs?query=hr " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">HR Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/sales-jobs?query=sales " target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Sales Jobs
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion">
                                <div class="rotate-360 flex cursor-pointer justify-between border-b border-white/10 false p-4">
                                    <span class="text-sm font-normal text-content-primary">International Jobs</span>
                                    <svg fill="currentColor" viewBox="0 0 24 24" class="transition-transform duration-300 " width="16">
                                        <path fill-rule="evenodd" d="M5.47 9.47c.3-.3.77-.3 1.06 0L12 14.94l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6 6c-.3.3-.77.3-1.06 0l-6-6a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="transition-max-height max-h-0 overflow-hidden duration-300 ">
                                    <div class="flex flex-wrap gap-1   bg-surface-primary-normal px-3 pb-2 pt-3 text-content-primary">
                                        <a href="/search/jobs-in-gulf" target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Jobs in Gulf
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/jobs-in-singapore" target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Jobs in Singapore
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/jobs-in-malaysia" target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Jobs in Malaysia
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/jobs-in-philippines" target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Jobs in Philippines
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/jobs-in-hong-kong" target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Jobs in Hong Kong
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/jobs-in-vietnam" target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Jobs in Vietnam
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/jobs-in-indonesia" target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Jobs in Indonesia
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/jobs-in-thailand" target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Jobs in Thailand
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/jobs-in-dubai" target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Jobs in Dubai
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                        <a href="/search/jobs-in-uae" target="_self">
                                            <button class="inline-flex gap-1 items-center rounded-[20px] border-[1px] border-grey-300 px-3 py-2 bg-white cursor-default pill-item" tabindex="-1" type="button">
                                                <p class="display-name font-normal truncate text-xs">Jobs in UAE
                                                <!-- -->
                                                </p>
                                            </button>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--/$-->
                    </div>
                    <div class="m-auto flex w-[390px] flex-col items-center gap-4 md:!m-0 md:!items-end">
                        <div class="hidden h-[250px] text-center md:flex md:w-full md:items-center md:justify-center md:rounded-2xl">
                            <div class="flex justify-center ">
                                <div class="overflow-hidden " id="BB-Desktop-JD-Right"></div>
                            </div>
                        </div>
                        <div class="hidden w-full flex-col items-end gap-4 md:flex">
                            <div class="h-[242px] w-full rounded-2xl bg-decorative-surface-01 p-4">
                                <img alt="contract" loading="lazy" width="117" height="118" decoding="async" data-nimg="1" class="max-w-full" style="color:transparent" src="//media.foundit.in/public/core/images/jd/login.png"/>
                                <div>
                                    <div class="mb-2 text-sm font-semibold text-content-strong">Do you want to see more relevant and perfect job for you?</div>
                                    <div class="flex flex-row items-center gap-2 text-center">
                                        <a href="https://www.foundit.in/rio/login/seeker" class="flex flex-1" target="_blank">
                                            <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl text-sm font-normal bg-primary-400 hover:border-trusted-200 w-full rounded border border-border-strong px-2 py-1 text-brand-primary transition-all duration-300 ease-in-out hover:bg-brand-primary hover:text-surface-primary-normal" as="a">Login</button>
                                        </a>
                                        <a href="https://www.foundit.in/seeker/registration" target="_blank" class="flex flex-1">
                                            <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl text-sm font-normal bg-primary-200 border-primary-200 hover:bg-trusted-700 w-full rounded border !bg-brand-primary px-2 py-1 text-surface-primary-normal" as="a">Register</button>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="h-fit w-full overflow-hidden rounded-2xl border bg-decorative-surface-04 hover:cursor-pointer">
                                <img alt="Beware of Scammers" loading="lazy" width="300" height="300" decoding="async" data-nimg="1" class="w-full" style="color:transparent" src="https://media.foundit.in/public/core/images/jd/scam.svg"/>
                                <div class="px-5 pb-4 pt-1">
                                    <span class="mb-2 text-base font-bold text-decorative-content-04">Beware of Scammers</span>
                                    <p class="text-xs  font-normal text-content-secondary">We don’t charge any money for job offers</p>
                                </div>
                            </div>
                            <div class=" h-fit w-full overflow-hidden rounded-2xl bg-surface-primary-normal">
                                <img alt="Interview Calls" loading="lazy" width="300" height="300" decoding="async" data-nimg="1" class="w-full" style="color:transparent" src="https://media.foundit.in/public/core/images/jd/interview.svg"/>
                                <div class="h-[117px] px-4 pb-4">
                                    <p class="text-sm font-normal text-content-secondary">What it feels like to have</p>
                                    <span class="text-sm font-bold text-decorative-content-02">48% more interview calls?</span>
                                    <p class="mt-2 text-xs font-normal text-content-tertiary">To get 5X more recruiter views on your profile</p>
                                    <a href="https://www.foundit.in/career-services" target="_blank">
                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl text-sm font-normal bg-primary-400 hover:border-trusted-200 mt-2 w-full rounded border border-border-strong px-3 py-2 text-center text-content-strong hover:bg-brand-primary hover:text-surface-primary-normal">Ask here</button>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--$-->
        <div class="mb-[54px] md:mb-0 " id="seekerFooter">
            <footer class="w-full bg-darkKnight-600 p-5">
                <div class="container max-w-[1208px]">
                    <div class="md:flex">
                        <div class="border-gray-700 md:w-5/12 md:border-r">
                            <div class="flex flex-col gap-4 px-4 text-white">
                                <div class="accordion">
                                    <div class="rotate-360 flex cursor-pointer justify-between border-b border-white/10 pb-2">
                                        <p>Job Categories</p>
                                        <svg fill="currentColor" viewBox="0 0 24 24" class="transition-transform duration-300 " width="24">
                                            <path fill-rule="evenodd" d="M5.47 9.47c.3-.3.77-.3 1.06 0L12 14.94l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6 6c-.3.3-.77.3-1.06 0l-6-6a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="transition-max-height max-h-0 overflow-hidden duration-300 ">
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/jobs-by-location" class="text-sm">Jobs By Location</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/jobs-by-skill" class="text-sm">Jobs By Skill</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/jobs-by-title" class="text-sm">Jobs By Title</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/jobs-by-function" class="text-sm">Jobs By Function</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/jobs-by-industry" class="text-sm">Jobs By Industry</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/jobs-by-education" class="text-sm">Jobs By Education</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/jobs-by-other" class="text-sm">Jobs By Other</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion">
                                    <div class="rotate-360 flex cursor-pointer justify-between border-b border-white/10 pb-2">
                                        <p>Employers</p>
                                        <svg fill="currentColor" viewBox="0 0 24 24" class="transition-transform duration-300 " width="24">
                                            <path fill-rule="evenodd" d="M5.47 9.47c.3-.3.77-.3 1.06 0L12 14.94l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6 6c-.3.3-.77.3-1.06 0l-6-6a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="transition-max-height max-h-0 overflow-hidden duration-300 ">
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="https://recruiter.foundit.in/?login=yes" class="text-sm">Employer Login</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="https://recruiter.foundit.in/job-posting-services.html" class="text-sm">Job Posting</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="https://recruiter.foundit.in/v2/ecom/index.html" class="text-sm">Access Resume Database</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="https://recruiter.foundit.in/v2/recruiter-sign-up.html" class="text-sm">Join mRecruiters</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="https://recruiter.foundit.in/v2/ecom/index.html" class="text-sm">Buy Online</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion">
                                    <div class="rotate-360 flex cursor-pointer justify-between border-b border-white/10 pb-2">
                                        <p>Job Seekers</p>
                                        <svg fill="currentColor" viewBox="0 0 24 24" class="transition-transform duration-300 " width="24">
                                            <path fill-rule="evenodd" d="M5.47 9.47c.3-.3.77-.3 1.06 0L12 14.94l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6 6c-.3.3-.77.3-1.06 0l-6-6a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="transition-max-height max-h-0 overflow-hidden duration-300 ">
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/rio/login" class="text-sm">Job Seekers Login</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/seeker/registration" class="text-sm">Upload Resume</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/info/search-tips" class="text-sm">Search Tips</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/jobs-by-company" class="text-sm">Find Companies</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/info/help" class="text-sm">Help</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion">
                                    <div class="rotate-360 flex cursor-pointer justify-between border-b border-white/10 pb-2">
                                        <a target="_blank" href="/career-advice/">Career Advice</a>
                                    </div>
                                    <div class="transition-max-height max-h-0 overflow-hidden duration-300 "></div>
                                </div>
                                <div class="accordion">
                                    <div class="rotate-360 flex cursor-pointer justify-between border-b border-white/10 pb-2">
                                        <p>Company Info</p>
                                        <svg fill="currentColor" viewBox="0 0 24 24" class="transition-transform duration-300 " width="24">
                                            <path fill-rule="evenodd" d="M5.47 9.47c.3-.3.77-.3 1.06 0L12 14.94l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6 6c-.3.3-.77.3-1.06 0l-6-6a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="transition-max-height max-h-0 overflow-hidden duration-300 ">
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/info/about-us" class="text-sm">About Us</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/info/contact-us" class="text-sm">Contact Us</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/info/feedback" class="text-sm">Send Feedback</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/info/sitemap" class="text-sm">HTML Sitemap</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/xmlsitemap/sitemap-index.xml" class="text-sm">XML Sitemap</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion">
                                    <div class="rotate-360 flex cursor-pointer justify-between border-b border-white/10 pb-2">
                                        <p>IT Jobs</p>
                                        <svg fill="currentColor" viewBox="0 0 24 24" class="transition-transform duration-300 " width="24">
                                            <path fill-rule="evenodd" d="M5.47 9.47c.3-.3.77-.3 1.06 0L12 14.94l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6 6c-.3.3-.77.3-1.06 0l-6-6a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="transition-max-height max-h-0 overflow-hidden duration-300 ">
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/software-testing-jobs" class="text-sm">Software testing</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/sql-jobs" class="text-sm">SQL</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/oracle-jobs" class="text-sm">Oracle</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/graphic-design-jobs" class="text-sm">Graphic Design</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/digital-marketing-jobs" class="text-sm">Digital marketing</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion">
                                    <div class="rotate-360 flex cursor-pointer justify-between border-b border-white/10 pb-2">
                                        <p>Non IT Jobs</p>
                                        <svg fill="currentColor" viewBox="0 0 24 24" class="transition-transform duration-300 " width="24">
                                            <path fill-rule="evenodd" d="M5.47 9.47c.3-.3.77-.3 1.06 0L12 14.94l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6 6c-.3.3-.77.3-1.06 0l-6-6a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="transition-max-height max-h-0 overflow-hidden duration-300 ">
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/hr-jobs" class="text-sm">HR</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/sales-jobs" class="text-sm">Sales</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/accounting-jobs" class="text-sm">Accounting</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/call-center-jobs" class="text-sm">Call Center</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/electrical-engineering-jobs" class="text-sm">Electrical engineering</a>
                                        </div>
                                        <div class="flex flex-col gap-4 px-5 pt-2">
                                            <a target="_blank" href="/search/event-management-jobs" class="text-sm">Event management</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion">
                                    <div class="rotate-360 flex cursor-pointer justify-between border-b border-white/10 pb-2">
                                        <a target="_blank" href="/badminton">Partnerships</a>
                                    </div>
                                    <div class="transition-max-height max-h-0 overflow-hidden duration-300 "></div>
                                </div>
                            </div>
                        </div>
                        <div class="md:w-7/12">
                            <ul class="px-4">
                                <li class="border-b border-gray-700 pb-4 text-white">
                                    <label for="country" class="inline-block pr-2 text-base font-normal">Selected Country</label>
                                    <select id="country" name="country" class="rounded-md border bg-[#1D1934] p-1  text-white">
                                        <option value="//www.foundit.in" selected="">India</option>
                                        <option value="//www.founditgulf.com">Gulf</option>
                                        <option value="//www.foundit.hk">Hong Kong</option>
                                        <option value="//www.foundit.sg">Singapore</option>
                                        <option value="//www.foundit.com.ph">Philippines</option>
                                        <option value="//www.monster.co.th">Thailand</option>
                                        <option value="//www.monster.com.vn">Vietnam</option>
                                        <option value="//www.foundit.id">Indonesia</option>
                                        <option value="//www.foundit.my">Malaysia</option>
                                    </select>
                                </li>
                                <li class="border-b border-gray-700 py-4 text-white">
                                    <span class="inline-block align-middle">
                                        <svg viewBox="0 0 24 24" fill="currentColor" width="20">
                                            <g id="phone call">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.01785 3.43925C6.66313 3.25983 6.24421 3.25983 5.88949 3.43925C5.7381 3.51582 5.57189 3.6687 4.99876 4.24183L4.84113 4.39946C4.28856 4.95203 4.1091 5.13975 3.96801 5.39092C3.80697 5.6776 3.67871 6.17078 3.67969 6.49959C3.68057 6.79348 3.72874 6.97885 3.89274 7.55665C4.71968 10.4702 6.27975 13.2196 8.57499 15.5148C10.8702 17.8101 13.6197 19.3701 16.5332 20.1971C17.111 20.3611 17.2964 20.4093 17.5902 20.4101C17.9191 20.4111 18.4122 20.2829 18.6989 20.1218C18.9501 19.9807 19.1378 19.8013 19.6904 19.2487L19.848 19.0911C20.4211 18.5179 20.574 18.3517 20.6506 18.2003C20.83 17.8456 20.83 17.4267 20.6506 17.072C20.574 16.9206 20.4211 16.7544 19.848 16.1813L19.6531 15.9864C19.2765 15.6097 19.1677 15.5068 19.0745 15.4461C18.6602 15.1768 18.126 15.1768 17.7117 15.4461C17.6185 15.5068 17.5097 15.6097 17.1331 15.9864C17.1258 15.9937 17.1184 16.001 17.1111 16.0084C17.0189 16.1008 16.9197 16.2002 16.8011 16.2852C16.3765 16.5892 15.7939 16.6885 15.2925 16.5424C15.1529 16.5017 15.0384 16.4466 14.9339 16.3963C14.9265 16.3927 14.9192 16.3892 14.9119 16.3857C13.387 15.6535 11.9586 14.6558 10.6963 13.3935C9.43406 12.1313 8.4363 10.7028 7.70417 9.17797C7.70066 9.17067 7.69713 9.16333 7.69357 9.15594C7.64324 9.05139 7.58816 8.93697 7.54745 8.79732L8.21665 8.60224L7.54745 8.79732C7.4013 8.29595 7.50064 7.71334 7.80468 7.28874L7.80468 7.28874C7.88964 7.17009 7.98905 7.07092 8.08142 6.97877C8.08881 6.97139 8.09616 6.96406 8.10345 6.95677C8.48013 6.58009 8.58306 6.47134 8.64369 6.3781L8.64369 6.3781C8.91306 5.96379 8.91306 5.42968 8.64369 5.01537C8.58306 4.92213 8.48013 4.81338 8.10345 4.4367L7.90858 4.24183C7.33545 3.6687 7.16924 3.51582 7.01785 3.43925ZM5.21247 2.10073C5.99286 1.70601 6.91448 1.70601 7.69487 2.10073C8.09002 2.30059 8.4371 2.64823 8.88557 3.09741C8.91307 3.12495 8.94094 3.15287 8.96924 3.18117L9.16411 3.37604C9.18274 3.39467 9.20112 3.41303 9.21925 3.43114C9.51488 3.72645 9.74301 3.95435 9.90126 4.19774C10.4939 5.10921 10.4939 6.28426 9.90125 7.19573C9.74301 7.43912 9.51487 7.66702 9.21925 7.96233C9.20112 7.98044 9.18274 7.9988 9.16411 8.01743C9.10367 8.07787 9.07095 8.11072 9.04682 8.13651C9.03619 8.14787 9.03046 8.15445 9.02749 8.158L9.02487 8.16121L9.02426 8.16203L8.41447 7.72538L9.02426 8.16203C8.98827 8.21229 8.97021 8.31819 8.98751 8.37753L8.9876 8.37783C8.98843 8.3807 8.99 8.3861 8.99785 8.40416C9.00885 8.42946 9.02432 8.46196 9.05639 8.52874C9.71625 9.9031 10.6161 11.192 11.757 12.3329C12.8979 13.4738 14.1867 14.3736 15.5611 15.0334L15.2365 15.7096L15.5611 15.0334C15.6279 15.0655 15.6604 15.081 15.6857 15.092C15.7037 15.0998 15.7091 15.1014 15.712 15.1022L15.7123 15.1023C15.7716 15.1196 15.8775 15.1016 15.9278 15.0656L15.9286 15.065L15.9318 15.0623C15.9354 15.0594 15.942 15.0536 15.9533 15.043C15.9791 15.0189 16.012 14.9862 16.0724 14.9257C16.091 14.9071 16.1094 14.8887 16.1275 14.8706C16.4228 14.5749 16.6507 14.3468 16.8941 14.1886C17.8056 13.596 18.9806 13.596 19.8921 14.1886C20.1355 14.3468 20.3634 14.575 20.6587 14.8706C20.6768 14.8887 20.6952 14.9071 20.7138 14.9257L20.9087 15.1206C20.937 15.1489 20.9649 15.1768 20.9924 15.2043C21.4416 15.6527 21.7892 15.9998 21.9891 16.395C22.3838 17.1754 22.3838 18.097 21.9891 18.8774C21.7892 19.2725 21.4416 19.6196 20.9924 20.0681C20.9649 20.0956 20.937 20.1234 20.9087 20.1517L20.751 20.3094C20.732 20.3284 20.7131 20.3473 20.6945 20.3659C20.2184 20.8423 19.8848 21.1761 19.4336 21.4296C18.9212 21.7174 18.1734 21.9119 17.5858 21.9101C17.0688 21.9086 16.6963 21.8028 16.17 21.6533L16.1236 21.6401C12.9703 20.7451 9.99505 19.0562 7.51433 16.5755C5.03361 14.0948 3.34473 11.1195 2.44973 7.96622C2.44531 7.95062 2.44092 7.93516 2.43656 7.91983C2.28706 7.39355 2.18124 7.02099 2.1797 6.50405C2.17795 5.91642 2.37242 5.16861 2.66022 4.65628L2.66022 4.65628C2.91371 4.20504 3.24755 3.8714 3.72391 3.39534C3.74254 3.37672 3.76139 3.35788 3.78047 3.3388L3.9381 3.18117C3.9664 3.15287 3.99427 3.12495 4.02177 3.09741C4.47024 2.64823 4.81732 2.30059 5.21247 2.10073L5.49904 2.6673L5.21247 2.10073Z" fill="currentColor"></path>
                                            </g>
                                        </svg>
                                    </span>
                                    <span class="mx-1"></span>
                                    <a href="tel:+91 80 6985 7811" class="text-white hover:text-white hover:underline" aria-label="contact-info">Toll No: +91 80 6985 7811</a>
                                    |<span class="mx-1"></span>
                                    <a href="tel:1800-419-6666" class="text-white hover:text-white hover:underline" aria-label="contact-info">Toll Free No: 1800-419-6666</a>
                                </li>
                                <li class="border-b border-gray-700 py-4 text-white">
                                    <span class="inline-block pr-3 align-middle">
                                        <svg fill="currentColor" viewBox="0 0 24 24" height="20">
                                            <path d="M6.77 3.25h10.46c.82 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 1.64.22.43.32.9.36 1.44a.7.7 0 0 1 .03.43c.02.45.02.98.02 1.6v6.46c0 .82 0 1.47-.04 2a3.84 3.84 0 0 1-.37 1.47c-.36.7-.93 1.28-1.64 1.64-.44.23-.92.32-1.47.37-.53.04-1.18.04-2 .04H6.77c-.81 0-1.47 0-2-.04a3.84 3.84 0 0 1-1.47-.37 3.75 3.75 0 0 1-1.64-1.64 3.85 3.85 0 0 1-.37-1.47c-.04-.53-.04-1.19-.04-2V8.77c0-.62 0-1.15.02-1.6a.75.75 0 0 1 .03-.43c.04-.53.14-1 .36-1.44.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04Zm-4.02 5.2v6.75c0 .85 0 1.45.04 *********.*********.***********.*********.***********.46.04 1.06.04 1.91.04h10.4c.85 0 1.45 0 1.9-.04.46-.04.72-.1.92-.2.42-.22.77-.57.98-.99.1-.2.17-.46.21-.91.04-.46.04-1.06.04-1.91V8.44l-6.98 4.89-.1.07c-.57.4-1 .7-1.5.83-.44.1-.9.1-1.34 0-.5-.13-.93-.44-1.5-.83l-.1-.07-6.98-4.89Zm18.44-1.8a2 2 0 0 0-.19-.67 2.25 2.25 0 0 0-.98-.98 2.4 2.4 0 0 0-.91-.21c-.46-.04-1.06-.04-1.91-.04H6.8c-.85 0-1.45 0-1.9.04-.46.04-.72.1-.92.2-.42.22-.77.57-.98.99a2 2 0 0 0-.19.67l7.79 5.45c.7.5.9.62 1.1.67.2.05.4.05.6 0 .2-.05.4-.17 1.1-.67l7.79-5.45Z"></path>
                                        </svg>
                                    </span>
                                    <a href="mailto:<EMAIL>" class="text-white hover:text-white hover:underline" aria-label="mail"><EMAIL></a>
                                </li>
                                <li class="flex items-center gap-2 border-b border-gray-700 py-4 text-white">
                                    <span class="inline-block align-middle">
                                        <svg fill="currentColor" viewBox="0 0 24 24" width="20">
                                            <path fill-rule="evenodd" d="M12 2.25c.41 0 .75.34.75.75v10.19l3.72-3.72a.75.75 0 1 1 1.06 1.06l-5 5c-.3.3-.77.3-1.06 0l-5-5a.75.75 0 1 1 1.06-1.06l3.72 3.72V3c0-.41.34-.75.75-.75Zm-9 12c.41 0 .75.34.75.75v1.2c0 .85 0 1.45.04 *********.*********.***********.*********.***********.46.04 1.06.04 1.91.04h8.4c.85 0 1.45 0 1.9-.04.46-.04.72-.1.92-.2.42-.22.77-.57.98-.99.1-.2.17-.46.21-.91.04-.46.04-1.06.04-1.91V15a.75.75 0 0 1 1.5 0v1.23c0 .82 0 1.47-.04 2a3.84 3.84 0 0 1-.37 1.47c-.36.7-.93 1.28-1.64 1.64-.44.23-.92.32-1.47.37-.53.04-1.18.04-2 .04H7.77c-.81 0-1.47 0-2-.04a3.84 3.84 0 0 1-1.47-.37 3.75 3.75 0 0 1-1.64-1.64 3.85 3.85 0 0 1-.37-1.47c-.04-.53-.04-1.19-.04-2V15c0-.41.34-.75.75-.75Z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                    Download The App
                                    <span class="flex items-center gap-2">
                                        <a rel="noreferrer" target="_blank" href="https://apps.apple.com/in/app/monster-jobs/id525775161">
                                            <img src="https://media.foundit.in/public/core/images/appstore.svg" alt="IOS Download" width="66" height="20"/>
                                        </a>
                                        <a rel="noreferrer" target="_blank" href="https://play.google.com/store/apps/details?id=com.monsterindia.seeker.views">
                                            <img src="https://media.foundit.in/public/core/images/playstore.svg" alt="Android Download" width="66" height="20"/>
                                        </a>
                                    </span>
                                </li>
                                <li class="border-b border-gray-700 py-4 text-white">
                                    <span class="inline-block pr-3 align-middle">Stay Connected</span>
                                    <span class="inline-block pr-3 align-middle">
                                        <a href="https://www.facebook.com/founditIN/" target="_blank" aria-label="stay-connected">
                                            <svg fill="currentColor" viewBox="0 0 24 24" width="22">
                                                <path d="M11.67 2a10.1 10.1 0 0 0-9.64 9.29 10 10 0 0 0 8.48 10.59v-7.23H8.89a1.31 1.31 0 1 1 0-2.63h1.62v-1.75c0-2.9 1.4-4.16 3.81-4.16.36 0 .67 0 .92.02a1.2 1.2 0 0 1-.07 2.4h-.44c-1.02 0-1.38.96-1.38 2.06v1.43h1.87c.6 0 1.05.53.96 1.11l-.11.7a.96.96 0 0 1-.96.82h-1.76v7.25A10 10 0 0 0 11.67 2Z"></path>
                                            </svg>
                                        </a>
                                    </span>
                                    <span class="inline-block pr-3 align-middle">
                                        <a href="https://twitter.com/foundit_India" target="_blank" aria-label="stay-connected">
                                            <svg fill="currentColor" viewBox="0 0 24 24" width="22">
                                                <path d="M17 3H7a4 4 0 0 0-4 4v10a4 4 0 0 0 4 4h10a4 4 0 0 0 4-4V7a4 4 0 0 0-4-4Zm.05 6.51v.35a6.99 6.99 0 0 1-10.8 5.91h.6c1.11 0 2.23-.43 3.09-1.03a2.5 2.5 0 0 1-2.32-1.71c.17 0 .34.09.43.09.26 0 .43 0 .69-.1a2.48 2.48 0 0 1-1.97-2.4c.34.18.68.27 1.11.35-.69-.6-1.11-1.28-1.11-2.14 0-.43.08-.86.34-1.2 1.2 1.46 3 2.49 5.06 2.57 0-.17-.09-.34-.09-.6a2.49 2.49 0 0 1 2.49-2.49c.68 0 1.37.26 1.8.77a4 4 0 0 0 1.54-.6 2.5 2.5 0 0 1-1.11 1.38c.5-.09.94-.18 1.45-.43-.34.51-.77.94-1.2 1.28Z"></path>
                                            </svg>
                                        </a>
                                    </span>
                                    <span class="inline-block pr-3 align-middle">
                                        <a href="https://www.linkedin.com/company/foundit-jobs/" target="_blank" aria-label="stay-connected">
                                            <svg fill="currentColor" viewBox="0 0 24 24" width="22">
                                                <path d="M19 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2ZM7.74 17c-.7 0-1.26-.57-1.26-1.26v-4.48a1.26 1.26 0 0 1 2.52 0v4.48c0 .7-.56 1.26-1.26 1.26Zm-.05-8.28c-.77 0-1.28-.52-1.28-1.2 0-.69.51-1.2 1.37-1.2.77 0 1.28.51 1.28 1.2 0 .68-.5 1.2-1.37 1.2ZM16.78 17c-.68 0-1.22-.55-1.22-1.22v-2.6c0-1.06-.65-1.18-.9-1.18-.24 0-1.05.04-1.05 1.17v2.6c0 .68-.55 1.23-1.23 1.23h-.08c-.67 0-1.22-.55-1.22-1.22v-4.52a1.26 1.26 0 1 1 2.53 0S13.89 10 15.8 10c1.21 0 2.19.98 2.19 3.17v2.6c0 .68-.55 1.23-1.22 1.23Z"></path>
                                            </svg>
                                        </a>
                                    </span>
                                    <span class="inline-block pr-3 align-middle">
                                        <a href="https://www.instagram.com/foundit_india/" target="_blank" aria-label="stay-connected">
                                            <svg fill="currentColor" viewBox="0 0 24 24" width="22">
                                                <path d="M8 3a5 5 0 0 0-5 5v8a5 5 0 0 0 5 5h8a5 5 0 0 0 5-5V8a5 5 0 0 0-5-5H8Zm10 2a1 1 0 1 1 0 2 1 1 0 0 1 0-2Zm-6 2a5 5 0 1 1 0 10 5 5 0 0 1 0-10Zm0 2a3 3 0 1 0 0 6 3 3 0 0 0 0-6Z"></path>
                                            </svg>
                                        </a>
                                    </span>
                                    <span class="inline-block pr-3 align-middle">
                                        <a href="https://www.youtube.com/@foundit_IN/featured" target="_blank" aria-label="stay-connected">
                                            <svg viewBox="0 0 24 24" fill="currentColor" width="22">
                                                <path d="M21.58 6.19a2.5 2.5 0 0 0-1.77-1.77C18.25 4 12 4 12 4s-6.25 0-7.81.42a2.5 2.5 0 0 0-1.77 1.77C2 7.75 2 12 2 12s0 4.25.42 5.81c.23.86.9 1.54 1.77 1.77C5.75 20 12 20 12 20s6.25 0 7.81-.42a2.5 2.5 0 0 0 1.77-1.77C22 16.25 22 12 22 12s0-4.25-.42-5.81ZM10 15.46V8.54L16 12l-6 3.46Z"></path>
                                            </svg>
                                        </a>
                                    </span>
                                    <span class="inline-block pr-3 align-middle">
                                        <a href="https://plus.google.com/u/0/b/105215280255599273934/105215280255599273934/posts" target="_blank" aria-label="stay-connected"></a>
                                    </span>
                                </li>
                                <li class="py-4 text-white">
                                    <span>
                                        <span class="mx-1">|</span>
                                        <a href="https://www.foundit.in/info/security-center" target="_blank" class="mb-2 inline-block text-white hover:text-white hover:underline" aria-label="legal">Security &amp;Fraud</a>
                                    </span>
                                    <span>
                                        <span class="mx-1">|</span>
                                        <a href="https://www.foundit.in/info/privacy" target="_blank" class="mb-2 inline-block text-white hover:text-white hover:underline" aria-label="legal">Privacy Notice</a>
                                    </span>
                                    <span>
                                        <span class="mx-1">|</span>
                                        <a href="https://www.foundit.in/info/terms-of-use" target="_blank" class="mb-2 inline-block text-white hover:text-white hover:underline" aria-label="legal">Terms of Use</a>
                                    </span>
                                    <span>
                                        <span class="mx-1">|</span>
                                        <a href="https://www.foundit.in/info/beware-of-fraudsters" target="_blank" class="mb-2 inline-block text-white hover:text-white hover:underline" aria-label="legal">Beware of Fraudsters</a>
                                    </span>
                                    <span>
                                        <span class="mx-1">|</span>
                                        <a href="https://www.foundit.in/info/besafe" target="_blank" class="mb-2 inline-block text-white hover:text-white hover:underline" aria-label="legal">Be Safe</a>
                                    </span>
                                    <span>
                                        <span class="mx-1">|</span>
                                        <a href="https://www.foundit.in/info/complaints" target="_blank" class="mb-2 inline-block text-white hover:text-white hover:underline" aria-label="legal">Complaints</a>
                                    </span>
                                    <span class="mx-2">|</span>
                                    <p>© 2024 foundit | All rights Reserved</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
        <!--/$-->
        <noscript>
            <iframe title="Google Tag Manager" src="https://www.googletagmanager.com/ns.html?id=GTM-NGL6ZM" height="0" width="0" style="display:none;visibility:hidden"></iframe>
        </noscript>
        <script src="https://media.foundit.in/public/core/_next/static/chunks/webpack-37cdbaff12b6db06.js" async=""></script>
        <script>
            (self.__next_f = self.__next_f || []).push([0]);
            self.__next_f.push([2, null])
        </script>
        <script>
            self.__next_f.push([1, "1:HL[\"https://media.foundit.in/public/core/_next/static/media/c4250770ab8708b6-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"https://media.foundit.in/public/core/_next/static/css/d97ec4ca882e9b24.css\",\"style\"]\n3:HL[\"https://media.foundit.in/public/core/_next/static/css/be9e52fd61916c77.css\",\"style\"]\n4:HL[\"https://media.foundit.in/public/core/_next/static/css/c06e7e4de5c17984.css\",\"style\"]\n5:HL[\"https://media.foundit.in/public/core/_next/static/css/043491062fceef02.css\",\"style\"]\n6:HL[\"https://media.foundit.in/public/core/_next/static/css/13dbffa19191361b.css\",\"style\"]\n"])
        </script>
        <script>
            self.__next_f.push([1, "7:I[2846,[],\"\"]\na:I[4707,[],\"\"]\nc:I[6423,[],\"\"]\nf:I[7063,[\"470\",\"static/chunks/app/global-error-3e48fa67b6e1c02e.js\"],\"default\"]\nb:[\"slug\",\"python-developer-heptarc-technology-solutions-private-limited-hyderabad-secunderabad-telangana-********\",\"c\"]\n10:[]\n"])
        </script>
        <script>
            self.__next_f.push([1, "0:[\"$\",\"$L7\",null,{\"buildId\":\"HQ59LFe5cx3FsofNayche\",\"assetPrefix\":\"https://media.foundit.in/public/core\",\"urlParts\":[\"\",\"job\",\"python-developer-heptarc-technology-solutions-private-limited-hyderabad-secunderabad-telangana-********?searchId=62b0c563-d2cb-49f6-a563-c29e73587d29\u0026child_search_id=b6d5206e-f5da-4db8-a7cb-e2cac7f3e51f\"],\"initialTree\":[\"\",{\"children\":[\"(Engagement)\",{\"children\":[\"job\",{\"children\":[[\"slug\",\"python-developer-heptarc-technology-solutions-private-limited-hyderabad-secunderabad-telangana-********\",\"c\"],{\"children\":[\"__PAGE__?{\\\"searchId\\\":\\\"62b0c563-d2cb-49f6-a563-c29e73587d29\\\",\\\"child_search_id\\\":\\\"b6d5206e-f5da-4db8-a7cb-e2cac7f3e51f\\\"}\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"(Engagement)\",{\"children\":[\"job\",{\"children\":[[\"slug\",\"python-developer-heptarc-technology-solutions-private-limited-hyderabad-secunderabad-telangana-********\",\"c\"],{\"children\":[\"__PAGE__\",{},[[\"$L8\",\"$L9\",[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://media.foundit.in/public/core/_next/static/css/043491062fceef02.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://media.foundit.in/public/core/_next/static/css/13dbffa19191361b.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]]],null],null]},[null,[\"$\",\"$La\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"(Engagement)\",\"children\",\"job\",\"children\",\"$b\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lc\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\"}]],null]},[null,[\"$\",\"$La\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"(Engagement)\",\"children\",\"job\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lc\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\"}]],null]},[null,[\"$\",\"$La\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"(Engagement)\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lc\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[]}]],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://media.foundit.in/public/core/_next/static/css/d97ec4ca882e9b24.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://media.foundit.in/public/core/_next/static/css/be9e52fd61916c77.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://media.foundit.in/public/core/_next/static/css/c06e7e4de5c17984.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],\"$Ld\"],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$Le\"],\"globalErrorComponent\":\"$f\",\"missingSlots\":\"$W10\"}]\n"])
        </script>
        <script>
            self.__next_f.push([1, "e:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"Python Developer Job for 5 - 7 Year of Exp In Heptarc Technology Solutions Private Limited Hyderabad / Secunderabad, Telangana,Bengaluru / Bangalore - ******** | foundit india \"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Job Description For Python Developer Posted by Heptarc Technology Solutions Private Limited For Hyderabad / Secunderabad, Telangana,Bengaluru / Bangalore. Require 5 years Experience With Other Qualification. Apply Now To This And Other Similar Jobs ! \"}],[\"$\",\"meta\",\"4\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"5\",{\"rel\":\"canonical\",\"href\":\"https://www.foundit.in/job/python-developer-heptarc-technology-solutions-private-limited-hyderabad-secunderabad-telangana-********\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:title\",\"content\":\"Python Developer Job for 5 - 7 Year of Exp In Heptarc Technology Solutions Private Limited Hyderabad / Secunderabad, Telangana,Bengaluru / Bangalore - ******** | foundit india \"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:description\",\"content\":\"Job Description For Python Developer Posted by Heptarc Technology Solutions Private Limited For Hyderabad / Secunderabad, Telangana,Bengaluru / Bangalore. Require 5 years Experience With Other Qualification. Apply Now To This And Other Similar Jobs ! \"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:url\",\"content\":\"https://www.foundit.in/job/python-developer-heptarc-technology-solutions-private-limited-hyderabad-secunderabad-telangana-********\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:site_name\",\"content\":\"foundit india\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"11\",{\"name\":\"twitter:card\",\"content\":\"summary\"}],[\"$\",\"meta\",\"12\",{\"name\":\"twitter:title\",\"content\":\"Python Developer Job for 5 - 7 Year of Exp In Heptarc Technology Solutions Private Limited Hyderabad / Secunderabad, Telangana,Bengaluru / Bangalore - ******** | foundit india \"}],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:description\",\"content\":\"Job Description For Python Developer Posted by Heptarc Technology Solutions Private Limited For Hyderabad / Secunderabad, Telangana,Bengaluru / Bangalore. Require 5 years Experience With Other Qualification. Apply Now To This And Other Similar Jobs ! \"}],[\"$\",\"link\",\"14\",{\"rel\":\"icon\",\"href\":\"/home/<USER>",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"meta\",\"15\",{\"name\":\"next-size-adjust\"}]]\n"])
        </script>
        <script>
            self.__next_f.push([1, "8:null\n"])
        </script>
        <script>
            self.__next_f.push([1, "11:I[2094,[\"185\",\"static/chunks/app/layout-a565af9c1482ec4e.js\"],\"Provider\"]\n12:I[9653,[\"185\",\"static/chunks/app/layout-a565af9c1482ec4e.js\"],\"HeaderComponent\"]\n13:I[3490,[\"601\",\"static/chunks/app/error-7ce573952a375044.js\"],\"default\"]\n18:\"$Sreact.suspense\"\n19:I[49,[\"358\",\"static/chunks/app/(Engagement)/job/%5B...slug%5D/page-8ded5f783839f2d3.js\"],\"PreloadCss\"]\n1a:I[4780,[\"185\",\"static/chunks/app/layout-a565af9c1482ec4e.js\"],\"default\"]\n1b:I[1778,[\"358\",\"static/chunks/app/(Engagement)/job/%5B...slug%5D/page-8ded5f783839f2d3.js\"],\"default\"]\n20:I[1331,[\"358\",\"static/chunks/app/(Engagement)/job/%5B...slug%5D/page-8ded5f783839f2d3.js\"],\"default\"]\n21:I[5878,[\"358\",\"static/chunks/app/(Engagement)/job/%5B...slug%5D/page-8ded5f783839f2d3.js\"],\"Image\"]\n22:I[8281,[\"358\",\"static/chunks/app/(Engagement)/job/%5B...slug%5D/page-8ded5f783839f2d3.js\"],\"default\"]\n14:{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"}\n15:{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"}\n16:{\"display\":\"inline-block\"}\n17:{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0}\n"])
        </script>
        <script>
            self.__next_f.push([1, "d:[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"__className_151f7e\",\"children\":[[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"media.foundit.in\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"link\",null,{\"rel\":\"dns-prefetch\",\"href\":\"media.foundit.in\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://pagead2.googlesyndication.com/\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"link\",null,{\"rel\":\"dns-prefetch\",\"href\":\"https://pagead2.googlesyndication.com/\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://www.google-analytics.com/\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"link\",null,{\"rel\":\"dns-prefetch\",\"href\":\"https://www.google-analytics.com/\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://connect.facebook.net/\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"link\",null,{\"rel\":\"dns-prefetch\",\"href\":\"https://connect.facebook.net/\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://www.googletagmanager.com/\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"link\",null,{\"rel\":\"dns-prefetch\",\"href\":\"https://www.googletagmanager.com/\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://www.googletagservices.com/\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"link\",null,{\"rel\":\"dns-prefetch\",\"href\":\"https://www.googletagservices.com/\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://googleads.g.doubleclick.net/\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"link\",null,{\"rel\":\"dns-prefetch\",\"href\":\"https://googleads.g.doubleclick.net/\"}],[\"$\",\"link\",null,{\"rel\":\"preload\",\"href\":\"https://media.foundit.in/public/core/_next/static/media/103fb0c975d13c4f-s.p.woff2\",\"as\":\"font\",\"type\":\"font/woff\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"link\",null,{\"rel\":\"preload\",\"href\":\"https://media.foundit.in/public/core/_next/static/media/slick.653a4cbb.woff\",\"as\":\"font\",\"type\":\"font/woff\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"meta\",null,{\"name\":\"facebook-domain-verification\",\"content\":\"d98wmjxybl8zhqmj01l5uy9kxtxk19\"}],[\"$\",\"meta\",null,{\"httpEquiv\":\"Content-Type\",\"content\":\"text/html; charset=utf-8\"}],[\"$\",\"meta\",null,{\"httpEquiv\":\"X-UA-Compatible\",\"content\":\"IE=9; IE=8; IE=7; IE=EDGE; chrome=1\"}],[\"$\",\"body\",null,{\"className\":\"overflow-x-hidden\",\"children\":[null,false,[\"$\",\"$L11\",null,{\"children\":[[\"$\",\"$L12\",null,{\"headerData\":[{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"site_id\":1,\"name\":\"Jobs\",\"id\":4098,\"link_rewrite\":\"/seeker/dashboard\",\"featured_media\":0,\"sticky\":false,\"children\":[{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"parent_id\":4098,\"site_id\":1,\"name\":\"Jobs for YOU\",\"id\":4100,\"link_rewrite\":\"/seeker/dashboard\",\"featured_media\":0,\"sticky\":false},{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"parent_id\":4098,\"site_id\":1,\"name\":\"Track Applied Jobs\",\"id\":4101,\"link_rewrite\":\"/seeker/dashboard?application_source=Organic\u0026activeTab=applied\",\"featured_media\":0,\"sticky\":false}]},{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"site_id\":1,\"name\":\"Highlight\",\"id\":4008,\"link_rewrite\":\"/career-services/boost\",\"featured_media\":0,\"sticky\":false,\"children\":[{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"parent_id\":4008,\"site_id\":1,\"name\":\"Featured profile\",\"id\":4009,\"link_rewrite\":\"/career-services/featured-profile-services\",\"featured_media\":0,\"sticky\":false},{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"parent_id\":4008,\"site_id\":1,\"name\":\"Profile highlighter\",\"id\":4091,\"link_rewrite\":\"/career-services/profile-highlighter\",\"featured_media\":0,\"sticky\":false},{\"module_tag\":\"Best Seller\",\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"parent_id\":4008,\"site_id\":1,\"name\":\"Career Booster\",\"id\":4754,\"link_rewrite\":\"/career-services/career-booster\",\"featured_media\":0,\"sticky\":false},{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"parent_id\":4008,\"site_id\":1,\"name\":\"Resume Writing\",\"id\":4093,\"link_rewrite\":\"/career-services/resume-services\",\"featured_media\":0,\"sticky\":false},{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"parent_id\":4008,\"site_id\":1,\"name\":\"Resume builder\",\"id\":4095,\"link_rewrite\":\"/career-services/premium-resume-builder\",\"featured_media\":0,\"sticky\":false},{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"parent_id\":4008,\"site_id\":1,\"name\":\"Linkedin Makeover\",\"id\":4097,\"link_rewrite\":\"/career-services/linkedin-profile-makeover\",\"featured_media\":0,\"sticky\":false}]},{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"site_id\":1,\"name\":\"Prep\",\"id\":4102,\"link_rewrite\":\"/career-services/prep\",\"featured_media\":0,\"sticky\":false,\"children\":[{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"parent_id\":4102,\"site_id\":1,\"name\":\"Mock interview\",\"id\":4103,\"link_rewrite\":\"/career-services/mock-interview\",\"featured_media\":0,\"sticky\":false},{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"parent_id\":4102,\"site_id\":1,\"name\":\"Interview Preparation\",\"id\":4104,\"link_rewrite\":\"/career-services/interview-preparation\",\"featured_media\":0,\"sticky\":false}]},{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"site_id\":1,\"name\":\"Learn\",\"id\":4105,\"link_rewrite\":\"/career-services/learn\",\"featured_media\":0,\"sticky\":false,\"children\":[{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"parent_id\":4105,\"site_id\":1,\"name\":\"Degree Programs\",\"id\":4108,\"link_rewrite\":\"/career-services/online-degree-programs\",\"featured_media\":0,\"sticky\":false},{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"parent_id\":4105,\"site_id\":1,\"name\":\"Certification\",\"id\":4809,\"link_rewrite\":\"/career-services/certification-programs\",\"featured_media\":0,\"sticky\":false}]},{\"menu_group\":\"mSiteReactTopLinks\",\"menu_type\":\"link\",\"site_id\":1,\"name\":\"Career Advice\",\"id\":4109,\"link_rewrite\":\"/career-advice/\",\"featured_media\":0,\"sticky\":false}],\"host\":\"www.foundit.in\",\"isLoggedIn\":false,\"isMobile\":false,\"hamburgerData\":null}],[\"$\",\"section\",null,{\"children\":[\"$\",\"$La\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$13\",\"errorStyles\":[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://media.foundit.in/public/core/_next/static/css/13dbffa19191361b.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],\"errorScripts\":[],\"template\":[\"$\",\"$Lc\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":\"$14\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":\"$15\",\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":\"$16\",\"children\":[\"$\",\"h2\",null,{\"style\":\"$17\",\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[]}]}],[\"$\",\"$18\",null,{\"fallback\":null,\"children\":[[\"$\",\"$L19\",null,{\"moduleIds\":[\"app/layout.tsx -\u003e @/app/components/common/Footer/Footer\"]}],[\"$\",\"$L1a\",null,{\"host\":\"www.foundit.in\",\"footerData\":{\"MODULE_LEFT_FOOTER_REACT_CORE\":[{\"name\":\"Job Categories\",\"children\":[{\"name\":\"Jobs By Location\",\"link_rewrite\":\"/search/jobs-by-location\"},{\"name\":\"Jobs By Skill\",\"link_rewrite\":\"/search/jobs-by-skill\"},{\"name\":\"Jobs By Title\",\"link_rewrite\":\"/search/jobs-by-title\"},{\"name\":\"Jobs By Function\",\"link_rewrite\":\"/search/jobs-by-function\"},{\"name\":\"Jobs By Industry\",\"link_rewrite\":\"/search/jobs-by-industry\"},{\"name\":\"Jobs By Education\",\"link_rewrite\":\"/search/jobs-by-education\"},{\"name\":\"Jobs By Other\",\"link_rewrite\":\"/search/jobs-by-other\"}]},{\"name\":\"Employers\",\"children\":[{\"name\":\"Employer Login\",\"link_rewrite\":\"https://recruiter.foundit.in/?login=yes\"},{\"name\":\"Job Posting\",\"link_rewrite\":\"https://recruiter.foundit.in/job-posting-services.html\"},{\"name\":\"Access Resume Database\",\"link_rewrite\":\"https://recruiter.foundit.in/v2/ecom/index.html\"},{\"name\":\"Join mRecruiters\",\"link_rewrite\":\"https://recruiter.foundit.in/v2/recruiter-sign-up.html\"},{\"name\":\"Buy Online\",\"link_rewrite\":\"https://recruiter.foundit.in/v2/ecom/index.html\"}]},{\"name\":\"Job Seekers\",\"children\":[{\"name\":\"Job Seekers Login\",\"link_rewrite\":\"/rio/login\"},{\"name\":\"Upload Resume\",\"link_rewrite\":\"/seeker/registration\"},{\"name\":\"Search Tips\",\"link_rewrite\":\"/info/search-tips\"},{\"name\":\"Find Companies\",\"link_rewrite\":\"/search/jobs-by-company\"},{\"name\":\"Help\",\"link_rewrite\":\"/info/help\"}]},{\"name\":\"Career Advice\",\"link_rewrite\":\"/career-advice/\"},{\"name\":\"Company Info\",\"children\":[{\"name\":\"About Us\",\"link_rewrite\":\"/info/about-us\"},{\"name\":\"Contact Us\",\"link_rewrite\":\"/info/contact-us\"},{\"name\":\"Send Feedback\",\"link_rewrite\":\"/info/feedback\"},{\"name\":\"HTML Sitemap\",\"link_rewrite\":\"/info/sitemap\"},{\"name\":\"XML Sitemap\",\"link_rewrite\":\"/xmlsitemap/sitemap-index.xml\"}]},{\"name\":\"IT Jobs\",\"children\":[{\"name\":\"Software testing\",\"link_rewrite\":\"/search/software-testing-jobs\"},{\"name\":\"SQL\",\"link_rewrite\":\"/search/sql-jobs\"},{\"name\":\"Oracle\",\"link_rewrite\":\"/search/oracle-jobs\"},{\"name\":\"Graphic Design\",\"link_rewrite\":\"/search/graphic-design-jobs\"},{\"name\":\"Digital marketing\",\"link_rewrite\":\"/search/digital-marketing-jobs\"}]},{\"name\":\"Non IT Jobs\",\"children\":[{\"name\":\"HR\",\"link_rewrite\":\"/search/hr-jobs\"},{\"name\":\"Sales\",\"link_rewrite\":\"/search/sales-jobs\"},{\"name\":\"Accounting\",\"link_rewrite\":\"/search/accounting-jobs\"},{\"name\":\"Call Center\",\"link_rewrite\":\"/search/call-center-jobs\"},{\"name\":\"Electrical engineering\",\"link_rewrite\":\"/search/electrical-engineering-jobs\"},{\"name\":\"Event management\",\"link_rewrite\":\"/search/event-management-jobs\"}]},{\"name\":\"Partnerships\",\"link_rewrite\":\"/badminton\"}],\"MODULE_RIGHT_FOOTER_REACT_CORE\":{\"legal\":[{\"name\":\"Security \u0026 Fraud\",\"link_rewrite\":\"https://www.foundit.in/info/security-center\"},{\"name\":\"Privacy Notice\",\"link_rewrite\":\"https://www.foundit.in/info/privacy\"},{\"name\":\"Terms of Use\",\"link_rewrite\":\"https://www.foundit.in/info/terms-of-use\"},{\"name\":\"Beware of Fraudsters\",\"link_rewrite\":\"https://www.foundit.in/info/beware-of-fraudsters\"},{\"name\":\"Be Safe\",\"link_rewrite\":\"https://www.foundit.in/info/besafe\"},{\"name\":\"Complaints\",\"link_rewrite\":\"https://www.foundit.in/info/complaints\"}],\"stay_connected\":[{\"name\":\"Facebook\",\"link_rewrite\":\"https://www.facebook.com/founditIN/\"},{\"name\":\"Twitter\",\"link_rewrite\":\"https://twitter.com/foundit_India\"},{\"name\":\"Linkedin\",\"link_rewrite\":\"https://www.linkedin.com/company/foundit-jobs/\"},{\"name\":\"Instagram\",\"link_rewrite\":\"https://www.instagram.com/foundit_india/\"},{\"name\":\"Youtube\",\"link_rewrite\":\"https://www.youtube.com/@foundit_IN/featured\"},{\"name\":\"Google Plus\",\"link_rewrite\":\"https://plus.google.com/u/0/b/105215280255599273934/105215280255599273934/posts\"}],\"contact_info\":[{\"name\":\"Toll No: +91 80 6985 7811\",\"link_rewrite\":\"tel:+91 80 6985 7811\"},{\"name\":\"Toll Free No: 1800-419-6666\",\"link_rewrite\":\"tel:1800-419-6666\"}],\"mail\":[{\"name\":\"<EMAIL>\",\"link_rewrite\":\"mailto:<EMAIL>\"}],\"country_list\":[{\"id\":\"1\",\"value\":\"//www.foundit.in\",\"name\":\"India\"},{\"id\":\"2\",\"value\":\"//www.founditgulf.com\",\"name\":\"Gulf\"},{\"id\":\"3\",\"value\":\"//www.foundit.hk\",\"name\":\"Hong Kong\"},{\"id\":\"4\",\"value\":\"//www.foundit.sg\",\"name\":\"Singapore\"},{\"id\":\"5\",\"value\":\"//www.foundit.com.ph\",\"name\":\"Philippines\"},{\"id\":\"6\",\"value\":\"//www.monster.co.th\",\"name\":\"Thailand\"},{\"id\":\"7\",\"value\":\"//www.monster.com.vn\",\"name\":\"Vietnam\"},{\"id\":\"8\",\"value\":\"//www.foundit.id\",\"name\":\"Indonesia\"},{\"id\":\"9\",\"value\":\"//www.foundit.my\",\"name\":\"Malaysia\"}],\"downloadApp\":[{\"imgURL\":\"https://media.foundit.in/public/core/images/appstore.svg\",\"link_rewrite\":\"https://apps.apple.com/in/app/monster-jobs/id525775161\",\"imageName\":\"IOS Download\"},{\"imgURL\":\"https://media.foundit.in/public/core/images/playstore.svg\",\"link_rewrite\":\"https://play.google.com/store/apps/details?id=com.monsterindia.seeker.views\",\"imageName\":\"Android Download\"}]}},\"isLoggedIn\":false}]]}]]}],[\"$\",\"noscript\",null,{\"children\":[\"$\",\"iframe\",null,{\"title\":\"Google Tag Manager\",\"src\":\"https://www.googletagmanager.com/ns.html?id=GTM-NGL6ZM\",\"height\":\"0\",\"width\":\"0\",\"style\":{\"display\":\"none\",\"visibility\":\"hidden\"}}]}]]}]]}]\n"])
        </script>
        <script>
            self.__next_f.push([1, "1c:T4f1,{\"stopConsentAutoUpdate\":\"false\",\"showUserEngagement\":\"true\",\"heroSectionSubHeading\":\"openings delivered perfectly\",\"resumeParserSwitch\":\"false\",\"syndicatedQuickApplyEnabled\":\"true\",\"bulkApplyMultiSelect\":\"15\",\"oldLoginCheck\":\"true\",\"aspireSwitch\":\"false\",\"showUploadViaGoogleDrive\":\"false\",\"heroSectionHeading\":\"Over 7,00,000+\",\"gaAccountId\":\"UA-********-1\",\"hideCsCartIcon\":\"false\",\"triumphEventId\":\"2\",\"showAppRegistrationPopup\":\"true\",\"recaptchaV2Switch\":\"true\",\"srpBrandBooster\":\"false\",\"dfpSwitch\":\"true\",\"parseNewResume\":\"true\",\"parseResumeSwitchDesktop\":\"false\",\"hideExperienceTypePopup\":\"true\",\"cleverTapProjectId\":\"6K9-ZK8-ZZ6Z\",\"showLinkedInConsent\":\"true\",\"copyrightYear\":\"2020\",\"showDesktopDropboxV2\":\"false\",\"hideBellNotification\":\"false\",\"showPassiveJobs\":\"false\",\"triumphEndDate\":\"14-10-2024\",\"showAspireEnrollPopup\":\"false\",\"jobsCount\":\"Over 8,00,000\",\"showTriumphEvent\":\"true\",\"transparentLogoUrl\":\"https://media.foundit.in/trex/public/default/images/founditTransparentLogo.json\",\"showPromotionsBox\":\"false\",\"parseResumePhoenix\":\"true\",\"showAppLoginPopup\":\"false\",\"logoUrl\":\"https://media.foundit.in/trex/public/default/images/founditLogo.json\",\"resumeParseTime\":\"12000\",\"showAspireElement\":\"false\",\"socialLoginOn\":\"true\",\"newProfileFlow\":\"false\"}"])
        </script>
        <script>
            self.__next_f.push([1, "9:[false,[\"$\",\"$L1b\",null,{\"siteProps\":{\"site\":\"foundit\",\"tenant\":\"msite\",\"category\":\"Home mSite\",\"lang\":null,\"categoryId\":321,\"tenantId\":5,\"kiwiChannel\":null,\"kiwiSubChannel\":null,\"domain\":\"foundit.in\",\"country\":{\"id\":110,\"callPrefix\":\"91\",\"isoCode\":\"IN\"},\"currency\":{\"id\":4,\"name\":\"Indian Rupees\",\"isoCode\":\"INR\",\"sign\":\"₹\"},\"properties\":\"$1c\",\"festivePropImage\":null,\"festivePropBodyCss\":null,\"festivePropHeaderCss\":null,\"festivePropFooterCss\":null,\"context\":\"rexmonster\",\"favicon\":\"\",\"underMaintainance\":0,\"siteContextProperties\":{\"MONSTERHONGKONG_BASE_URL\":\"https://www.foundit.hk\",\"MONSTERMALAYSIA_BASE_URL\":\"https://www.foundit.my\",\"MONSTERTHAILAND_BASE_URL\":\"https://www.monster.co.th\",\"MONSTERPHILIP_BASE_URL\":\"https://www.foundit.com.ph\",\"MONSTERINDO_BASE_URL\":\"https://www.foundit.id\",\"MONSTERVIETNAM_BASE_URL\":\"https://www.monster.com.vn\",\"MONSTERGULF_BASE_URL\":\"https://www.founditgulf.com\",\"REXMONSTER_BASE_URL\":\"https://www.foundit.in\",\"MONSTERSINGAPORE_BASE_URL\":\"https://www.foundit.sg\"},\"contextSiteProperties\":{\"brandName\":\"foundit\",\"environment\":\"www\",\"brandNameKey\":null,\"propertyMap\":{\"HongKong\":{\"domain\":\"foundit.hk\",\"siteContext\":\"monsterhongkong\",\"textName\":\"FoundItHongKong\",\"channelName\":\"HongKong\"},\"Vietnam\":{\"domain\":\"monster.com.vn\",\"siteContext\":\"monstervietnam\",\"textName\":\"MonsterWebIn\",\"channelName\":\"Vietnam\"},\"Singapore\":{\"domain\":\"foundit.sg\",\"siteContext\":\"monstersingapore\",\"textName\":\"FoundItSingapore\",\"channelName\":\"Singapore\"},\"Philippines\":{\"domain\":\"foundit.com.ph\",\"siteContext\":\"monsterphilippines\",\"textName\":\"FoundItPhilippines\",\"channelName\":\"Philippines\"},\"Gulf\":{\"domain\":\"founditgulf.com\",\"siteContext\":\"monstergulf\",\"textName\":\"MonsterGulf\",\"channelName\":\"Gulf\"},\"Malaysia\":{\"domain\":\"foundit.my\",\"siteContext\":\"monstermalaysia\",\"textName\":\"FoundItMalaysia\",\"channelName\":\"Malaysia\"},\"Thailand\":{\"domain\":\"monster.co.th\",\"siteContext\":\"monsterthailand\",\"textName\":\"FoundItThailand\",\"channelName\":\"Thailand\"},\"India\":{\"domain\":\"foundit.in\",\"siteContext\":\"rexmonster\",\"textName\":\"FoundItIndia\",\"channelName\":\"India\"},\"Indonesia\":{\"domain\":\"foundit.id\",\"siteContext\":\"monsterindonesia\",\"textName\":\"FoundItIndonesia\",\"channelName\":\"Indonesia\"}}},\"mediaUrl\":\"media.foundit.in\",\"msuidCookie\":null,\"propertiesMap\":{\"stopConsentAutoUpdate\":\"false\",\"showUserEngagement\":\"true\",\"heroSectionSubHeading\":\"openings delivered perfectly\",\"resumeParserSwitch\":\"false\",\"syndicatedQuickApplyEnabled\":\"true\",\"bulkApplyMultiSelect\":\"15\",\"oldLoginCheck\":\"true\",\"aspireSwitch\":\"false\",\"showUploadViaGoogleDrive\":\"false\",\"heroSectionHeading\":\"Over 7,00,000+\",\"gaAccountId\":\"UA-********-1\",\"hideCsCartIcon\":\"false\",\"triumphEventId\":\"2\",\"showAppRegistrationPopup\":\"true\",\"recaptchaV2Switch\":\"true\",\"srpBrandBooster\":\"false\",\"dfpSwitch\":\"true\",\"parseNewResume\":\"true\",\"parseResumeSwitchDesktop\":\"false\",\"hideExperienceTypePopup\":\"true\",\"cleverTapProjectId\":\"6K9-ZK8-ZZ6Z\",\"showLinkedInConsent\":\"true\",\"copyrightYear\":\"2020\",\"showDesktopDropboxV2\":\"false\",\"hideBellNotification\":\"false\",\"showPassiveJobs\":\"false\",\"triumphEndDate\":\"14-10-2024\",\"showAspireEnrollPopup\":\"false\",\"jobsCount\":\"Over 8,00,000\",\"showTriumphEvent\":\"true\",\"transparentLogoUrl\":\"https://media.foundit.in/trex/public/default/images/founditTransparentLogo.json\",\"showPromotionsBox\":\"false\",\"parseResumePhoenix\":\"true\",\"showAppLoginPopup\":\"false\",\"logoUrl\":\"https://media.foundit.in/trex/public/default/images/founditLogo.json\",\"resumeParseTime\":\"12000\",\"showAspireElement\":\"false\",\"socialLoginOn\":\"true\",\"newProfileFlow\":\"false\"},\"mobile\":null},\"isMobile\":false}],[\"$\",\"div\",null,{\"id\":\"jdPage\",\"className\":\"w-full bg-surface-primary-normal pb-28 md:bg-surface-background md:pt-4\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col gap-6 md:m-auto md:max-w-[1203px] md:!flex-row lg:!flex-row\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex w-full max-w-[796px] flex-col gap-6\",\"children\":[\"$L1d\",[\"$\",\"$18\",null,{\"fallback\":null,\"children\":[[\"$\",\"$L19\",null,{\"moduleIds\":[\"app/(Engagement)/job/[...slug]/page.tsx -\u003e @/app/components/common/SimilarJobSection\"]}],\"$L1e\"]}],false,\"$L1f\"]}],[\"$\",\"div\",null,{\"className\":\"m-auto flex w-[390px] flex-col items-center gap-4 md:!m-0 md:!items-end\",\"children\":[[\"$\",\"div\",null,{\"className\":\"hidden h-[250px] text-center md:flex md:w-full md:items-center md:justify-center md:rounded-2xl\",\"children\":[\"$\",\"$L20\",null,{\"adName\":\"BB-Desktop-JD-Right\",\"adSize\":[300,250],\"isBBSlot\":true,\"isLoggedIn\":false,\"adPageName\":\"jdPage\"}]}],[\"$\",\"div\",null,{\"className\":\"hidden w-full flex-col items-end gap-4 md:flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-[242px] w-full rounded-2xl bg-decorative-surface-01 p-4\",\"children\":[[\"$\",\"$L21\",null,{\"className\":\"max-w-full\",\"src\":\"//media.foundit.in/public/core/images/jd/login.png\",\"alt\":\"contract\",\"width\":117,\"height\":118}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-2 text-sm font-semibold text-content-strong\",\"children\":\"Do you want to see more relevant and perfect job for you?\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-row items-center gap-2 text-center\",\"children\":[[\"$\",\"a\",null,{\"href\":\"https://www.foundit.in/rio/login/seeker\",\"className\":\"flex flex-1\",\"target\":\"_blank\",\"children\":[\"$\",\"button\",null,{\"type\":\"button\",\"className\":\"inline-flex items-center justify-center gap-1 rounded-3xl text-sm font-normal bg-primary-400 hover:border-trusted-200 w-full rounded border border-border-strong px-2 py-1 text-brand-primary transition-all duration-300 ease-in-out hover:bg-brand-primary hover:text-surface-primary-normal\",\"disabled\":false,\"as\":\"a\",\"children\":[\"\",\"Login\",\"\",false]}]}],[\"$\",\"a\",null,{\"href\":\"https://www.foundit.in/seeker/registration\",\"target\":\"_blank\",\"className\":\"flex flex-1\",\"children\":[\"$\",\"button\",null,{\"type\":\"button\",\"className\":\"inline-flex items-center justify-center gap-1 rounded-3xl text-sm font-normal bg-primary-200 border-primary-200 hover:bg-trusted-700 w-full rounded border !bg-brand-primary px-2 py-1 text-surface-primary-normal\",\"disabled\":false,\"as\":\"a\",\"children\":[\"\",\"Register\",\"\",false]}]}]]}]]}]]}],false,[\"$\",\"$L22\",null,{\"mediaUrl\":\"media.foundit.in\"}],[\"$\",\"div\",null,{\"className\":\" h-fit w-full overflow-hidden rounded-2xl bg-surface-primary-normal\",\"children\":[[\"$\",\"$L21\",null,{\"src\":\"https://media.foundit.in/public/core/images/jd/interview.svg\",\"width\":300,\"height\":300,\"alt\":\"Interview Calls\",\"className\":\"w-full\"}],[\"$\",\"div\",null,{\"className\":\"h-[117px] px-4 pb-4\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-sm font-normal text-content-secondary\",\"children\":\"What it feels like to have\"}],[\"$\",\"span\",null,{\"className\":\"text-sm font-bold text-decorative-content-02\",\"children\":\"48% more interview calls?\"}],[\"$\",\"p\",null,{\"className\":\"mt-2 text-xs font-normal text-content-tertiary\",\"children\":\"To get 5X more recruiter views on your profile\"}],[\"$\",\"a\",null,{\"href\":\"https://www.foundit.in/career-services\",\"target\":\"_blank\",\"children\":[\"$\",\"button\",null,{\"type\":\"button\",\"className\":\"inline-flex items-center justify-center gap-1 rounded-3xl text-sm font-normal bg-primary-400 hover:border-trusted-200 mt-2 w-full rounded border border-border-strong px-3 py-2 text-center text-content-strong hover:bg-brand-primary hover:text-surface-primary-normal\",\"disabled\":false,\"children\":[\"\",\"Ask here\",\"\",false]}]}]]}]]}]]}]]}]]}]}]]\n"])
        </script>
        <script>
            self.__next_f.push([1, "24:I[4064,[\"358\",\"static/chunks/app/(Engagement)/job/%5B...slug%5D/page-8ded5f783839f2d3.js\"],\"default\"]\n36:I[9585,[\"358\",\"static/chunks/app/(Engagement)/job/%5B...slug%5D/page-8ded5f783839f2d3.js\"],\"default\"]\n23:T8ab,"])
        </script>
        <script>
            self.__next_f.push([1, "{\"@context\":\"https://schema.org\",\"@type\":\"JobPosting\",\"title\":\"Python Developer\",\"description\":\"\u003cul\u003e\u003cli\u003eProven experience as a Data Engineer.\u003c/li\u003e\u003cli\u003eStrong expertise in Python and SQL.\u003c/li\u003e\u003cli\u003eExperience with ETL techniques and dealing with different data types and formats.\u003c/li\u003e\u003cli\u003eStrong knowledge of different database systems, both RDBMS SQL and NoSQL. Hive/HBase is an advantage\u003c/li\u003e\u003cli\u003eStrong knowledge and experience in using Cloud (AWS, Azure, GCP)\u003c/li\u003e\u003cli\u003eExperience with SDLC (Software Development Life Cycle) such as Agile Scrum, Kanban, Jira\u003c/li\u003e\u003cli\u003eFamiliarity with data processing tools and frameworks (e.g., Airflow, Hadoop, Spark).\u003c/li\u003e\u003cli\u003eExperience with Azure DevOps, Dockers, and Kubernetes is desired.\u003c/li\u003e\u003c/ul\u003e\u003cp\u003e\u003cstrong\u003eRequired Skills\u003c/strong\u003e AWS;Azure DevOps;Jira;Python;SQL\u003c/p\u003e\",\"identifier\":{\"@type\":\"PropertyValue\",\"name\":\"Heptarc Technology Solutions Private Limited\",\"value\":********},\"datePosted\":\"09-04-2025\",\"validThrough\":\"21-09-2025\",\"url\":\"www.foundit.in/job/python-developer-heptarc-technology-solutions-private-limited-hyderabad-secunderabad-telangana-********\",\"directApply\":true,\"employmentType\":\"Full time\",\"jobLocation\":{\"@type\":\"Place\",\"address\":[{\"@type\":\"PostalAddress\",\"addressLocality\":\"Hyderabad / Secunderabad, Telangana\",\"addressRegion\":\"Telangana\",\"addressCountry\":91,\"postalCode\":\"NA\",\"streetAddress\":\"NA\"},{\"@type\":\"PostalAddress\",\"addressLocality\":\"Bengaluru / Bangalore\",\"addressRegion\":\"Karnataka\",\"addressCountry\":91,\"postalCode\":\"NA\",\"streetAddress\":\"NA\"}]},\"jobLocationType\":\"\",\"hiringOrganization\":{\"name\":\"Heptarc Technology Solutions Private Limited\",\"sameAs\":\"Heptarc Technology Solutions Private Limited\",\"@type\":\"Organization\"},\"experienceRequirements\":{\"@type\":\"OccupationalExperienceRequirements\",\"monthsOfExperience\":60},\"educationRequirements\":\"\",\"qualifications\":\"NA\",\"occupationalCategory\":\"IT\",\"responsibilities\":[\"Database Administrator (DBA)\",\"Datawarehousing Consultants\"],\"industry\":[\"Software\"],\"skills\":[\"\",\"AWS\",\"Data Engineer\",\"Sdlc Concepts\",\"Azure Devops\"],\"baseSalary\":{\"@type\":\"MonetaryAmount\",\"currency\":\"INR\",\"value\":{\"@type\":\"QuantitativeValue\",\"minValue\":\"Salary Confidential\",\"maxValue\":\"Salary Confidential\",\"unitText\":\"YEAR\"}}}"])
        </script>
        <script>
            self.__next_f.push([1, "26:{\"id\":110,\"callPrefix\":\"91\",\"isoCode\":\"IN\"}\n27:{\"id\":4,\"name\":\"Indian Rupees\",\"isoCode\":\"INR\",\"sign\":\"₹\"}\n28:T4f1,{\"stopConsentAutoUpdate\":\"false\",\"showUserEngagement\":\"true\",\"heroSectionSubHeading\":\"openings delivered perfectly\",\"resumeParserSwitch\":\"false\",\"syndicatedQuickApplyEnabled\":\"true\",\"bulkApplyMultiSelect\":\"15\",\"oldLoginCheck\":\"true\",\"aspireSwitch\":\"false\",\"showUploadViaGoogleDrive\":\"false\",\"heroSectionHeading\":\"Over 7,00,000+\",\"gaAccountId\":\"UA-********-1\",\"hideCsCartIcon\":\"false\",\"triumphEventId\":\"2\",\"showAppRegistrationPopup\":\"true\",\"recaptchaV2Switch\":\"true\",\"srpBrandBooster\":\"false\",\"dfpSwitch\":\"true\",\"parseNewResume\":\"true\",\"parseResumeSwitchDesktop\":\"false\",\"hideExperienceTypePopup\":\"true\",\"cleverTapProjectId\":\"6K9-ZK8-ZZ6Z\",\"showLinkedInConsent\":\"true\",\"copyrightYear\":\"2020\",\"showDesktopDropboxV2\":\"false\",\"hideBellNotification\":\"false\",\"showPassiveJobs\":\"false\",\"triumphEndDate\":\"14-10-2024\",\"showAspireEnrollPopup\":\"false\",\"jobsCount\":\"Over 8,00,000\",\"showTriumphEvent\":\"true\",\"transparentLogoUrl\":\"https://media.foundit.in/trex/public/default/images/founditTransparentLogo.json\",\"showPromotionsBox\":\"false\",\"parseResumePhoenix\":\"true\",\"showAppLoginPopup\":\"false\",\"logoUrl\":\"https://media.foundit.in/trex/public/default/images/founditLogo.json\",\"resumeParseTime\":\"12000\",\"showAspireElement\":\"false\",\"socialLoginOn\":\"true\",\"newProfileFlow\":\"false\"}29:{\"MONSTERHONGKONG_BASE_URL\":\"https://www.foundit.hk\",\"MONSTERMALAYSIA_BASE_URL\":\"https://www.foundit.my\",\"MONSTERTHAILAND_BASE_URL\":\"https://www.monster.co.th\",\"MONSTERPHILIP_BASE_URL\":\"https://www.foundit.com.ph\",\"MONSTERINDO_BASE_URL\":\"https://www.foundit.id\",\"MONSTERVIETNAM_BASE_URL\":\"https://www.monster.com.vn\",\"MONSTERGULF_BASE_URL\":\"https://www.founditgulf.com\",\"REXMONSTER_BASE_URL\":\"https://www.foundit.in\",\"MONSTERSINGAPORE_BASE_URL\":\"https://www.foundit.sg\"}\n2c:{\"domain\":\"foundit.hk\",\"siteContext\":\"monsterhongkong\",\"textName\":\"FoundItHongKong\",\"channelName\":\"HongKong\"}\n2d:{\"domain\":\"monster.com.vn\",\"siteContext\":\"monstervietnam\",\"textName\":\"Mons"])
        </script>
        <script>
            self.__next_f.push([1, "terWebIn\",\"channelName\":\"Vietnam\"}\n2e:{\"domain\":\"foundit.sg\",\"siteContext\":\"monstersingapore\",\"textName\":\"FoundItSingapore\",\"channelName\":\"Singapore\"}\n2f:{\"domain\":\"foundit.com.ph\",\"siteContext\":\"monsterphilippines\",\"textName\":\"FoundItPhilippines\",\"channelName\":\"Philippines\"}\n30:{\"domain\":\"founditgulf.com\",\"siteContext\":\"monstergulf\",\"textName\":\"MonsterGulf\",\"channelName\":\"Gulf\"}\n31:{\"domain\":\"foundit.my\",\"siteContext\":\"monstermalaysia\",\"textName\":\"FoundItMalaysia\",\"channelName\":\"Malaysia\"}\n32:{\"domain\":\"monster.co.th\",\"siteContext\":\"monsterthailand\",\"textName\":\"FoundItThailand\",\"channelName\":\"Thailand\"}\n33:{\"domain\":\"foundit.in\",\"siteContext\":\"rexmonster\",\"textName\":\"FoundItIndia\",\"channelName\":\"India\"}\n34:{\"domain\":\"foundit.id\",\"siteContext\":\"monsterindonesia\",\"textName\":\"FoundItIndonesia\",\"channelName\":\"Indonesia\"}\n2b:{\"HongKong\":\"$2c\",\"Vietnam\":\"$2d\",\"Singapore\":\"$2e\",\"Philippines\":\"$2f\",\"Gulf\":\"$30\",\"Malaysia\":\"$31\",\"Thailand\":\"$32\",\"India\":\"$33\",\"Indonesia\":\"$34\"}\n2a:{\"brandName\":\"foundit\",\"environment\":\"www\",\"brandNameKey\":null,\"propertyMap\":\"$2b\"}\n35:{\"stopConsentAutoUpdate\":\"false\",\"showUserEngagement\":\"true\",\"heroSectionSubHeading\":\"openings delivered perfectly\",\"resumeParserSwitch\":\"false\",\"syndicatedQuickApplyEnabled\":\"true\",\"bulkApplyMultiSelect\":\"15\",\"oldLoginCheck\":\"true\",\"aspireSwitch\":\"false\",\"showUploadViaGoogleDrive\":\"false\",\"heroSectionHeading\":\"Over 7,00,000+\",\"gaAccountId\":\"UA-********-1\",\"hideCsCartIcon\":\"false\",\"triumphEventId\":\"2\",\"showAppRegistrationPopup\":\"true\",\"recaptchaV2Switch\":\"true\",\"srpBrandBooster\":\"false\",\"dfpSwitch\":\"true\",\"parseNewResume\":\"true\",\"parseResumeSwitchDesktop\":\"false\",\"hideExperienceTypePopup\":\"true\",\"cleverTapProjectId\":\"6K9-ZK8-ZZ6Z\",\"showLinkedInConsent\":\"true\",\"copyrightYear\":\"2020\",\"showDesktopDropboxV2\":\"false\",\"hideBellNotification\":\"false\",\"showPassiveJobs\":\"false\",\"triumphEndDate\":\"14-10-2024\",\"showAspireEnrollPopup\":\"false\",\"jobsCount\":\"Over 8,00,000\",\"showTriumphEvent\":\"true\",\"transparentLogoUrl\":\"https://media.foundit.in/trex/public/default/images/foun"])
        </script>
        <script>
            self.__next_f.push([1, "ditTransparentLogo.json\",\"showPromotionsBox\":\"false\",\"parseResumePhoenix\":\"true\",\"showAppLoginPopup\":\"false\",\"logoUrl\":\"https://media.foundit.in/trex/public/default/images/founditLogo.json\",\"resumeParseTime\":\"12000\",\"showAspireElement\":\"false\",\"socialLoginOn\":\"true\",\"newProfileFlow\":\"false\"}\n25:{\"site\":\"foundit\",\"tenant\":\"msite\",\"category\":\"Home mSite\",\"lang\":null,\"categoryId\":321,\"tenantId\":5,\"kiwiChannel\":null,\"kiwiSubChannel\":null,\"domain\":\"foundit.in\",\"country\":\"$26\",\"currency\":\"$27\",\"properties\":\"$28\",\"festivePropImage\":null,\"festivePropBodyCss\":null,\"festivePropHeaderCss\":null,\"festivePropFooterCss\":null,\"context\":\"rexmonster\",\"favicon\":\"\",\"underMaintainance\":0,\"siteContextProperties\":\"$29\",\"contextSiteProperties\":\"$2a\",\"mediaUrl\":\"media.foundit.in\",\"msuidCookie\":null,\"propertiesMap\":\"$35\",\"mobile\":null}\n"])
        </script>
        <script>
            self.__next_f.push([1, "1d:[\"$\",\"div\",null,{\"className\":\"jdSection flex flex-col gap-4 md:!gap-6\",\"children\":[[\"$\",\"div\",null,{\"id\":\"jobDetailContainer\",\"className\":\"bg-surface-primary-normal md:!rounded-2xl md:!border md:!border-solid md:!border-border-subtle\",\"children\":[[\"$\",\"section\",null,{\"children\":[[\"$\",\"script\",null,{\"type\":\"application/ld+json\",\"dangerouslySetInnerHTML\":{\"__html\":\"$23\"}}],[\"$\",\"script\",null,{\"type\":\"application/ld+json\",\"dangerouslySetInnerHTML\":{\"__html\":\"{\\\"@context\\\":\\\"https://schema.org\\\",\\\"@type\\\":\\\"BreadcrumbList\\\",\\\"itemListElement\\\":[{\\\"@type\\\":\\\"ListItem\\\",\\\"position\\\":1,\\\"item\\\":{\\\"@id\\\":\\\"https://www.foundit.in\\\",\\\"name\\\":\\\"Home\\\"}},{\\\"@type\\\":\\\"ListItem\\\",\\\"position\\\":2,\\\"item\\\":{\\\"@id\\\":\\\"https://www.foundit.in/search/jobs-in-hyderabad-secunderabad-telangana\\\",\\\"name\\\":\\\"Jobs in Hyderabad / Secunderabad, Telangana\\\"}},{\\\"@type\\\":\\\"ListItem\\\",\\\"position\\\":3,\\\"item\\\":{\\\"name\\\":\\\"Python Developer\\\"}}]}\"}}]]}],[\"$\",\"$L24\",null,{\"jobData\":{\"id\":\"********\",\"jobId\":********,\"companyLogoUrl\":\"https://media.monsterindia.com/logos/xeft_heptarcinx/jdlogo.gif\",\"companyId\":1059144,\"companyName\":\"Heptarc Technology Solutions Private Limited\",\"title\":\"Python Developer\",\"hideCompanyName\":0,\"isMicrosite\":0,\"micrositeUrl\":\"\",\"applyUrl\":\"\",\"redirectUrl\":\"\",\"jobSource\":\"ORGANIC\",\"description\":\"\u003cul\u003e\u003cli\u003eProven experience as a Data Engineer.\u003c/li\u003e\u003cli\u003eStrong expertise in Python and SQL.\u003c/li\u003e\u003cli\u003eExperience with ETL techniques and dealing with different data types and formats.\u003c/li\u003e\u003cli\u003eStrong knowledge of different database systems, both RDBMS SQL and NoSQL. Hive/HBase is an advantage\u003c/li\u003e\u003cli\u003eStrong knowledge and experience in using Cloud (AWS, Azure, GCP)\u003c/li\u003e\u003cli\u003eExperience with SDLC (Software Development Life Cycle) such as Agile Scrum, Kanban, Jira\u003c/li\u003e\u003cli\u003eFamiliarity with data processing tools and frameworks (e.g., Airflow, Hadoop, Spark).\u003c/li\u003e\u003cli\u003eExperience with Azure DevOps, Dockers, and Kubernetes is desired.\u003c/li\u003e\u003c/ul\u003e\u003cp\u003e\u003cstrong\u003eRequired Skills\u003c/strong\u003e AWS;Azure DevOps;Jira;Python;SQL\u003c/p\u003e\",\"locations\":[{\"stdCode\":40,\"country\":\"India\",\"latLon\":\"17.439930,78.498274\",\"city\":\"Hyderabad / Secunderabad, Telangana\",\"isoCode\":\"IN\",\"state\":\"Telangana\",\"isdCode\":91,\"uuid\":\"72da2468-1cbc-4a9a-863a-89e2cfd6502f\"},{\"stdCode\":80,\"country\":\"India\",\"latLon\":\"12.971599,77.594563\",\"city\":\"Bengaluru / Bangalore\",\"isoCode\":\"IN\",\"state\":\"Karnataka\",\"isdCode\":91,\"uuid\":\"67bfdcae-92cb-4783-bf40-37f639e57710\"}],\"minimumExperience\":{\"years\":5},\"maximumExperience\":{\"years\":7},\"jobSalaryConfidential\":true,\"minimumSalary\":{\"absoluteValue\":500000,\"currency\":\"INR\",\"absoluteMonthlyValue\":41667},\"maximumSalary\":{\"absoluteValue\":700000,\"currency\":\"INR\",\"absoluteMonthlyValue\":58333},\"totalApplicants\":12,\"freshness\":1753349010000,\"hideSalary\":1,\"currencyCode\":\"INR\",\"isGptwCompany\":0,\"jobTypes\":[\"Permanent Job\"],\"industries\":[\"Software\"],\"functions\":[\"IT\"],\"roles\":[\"Database Administrator (DBA)\",\"Datawarehousing Consultants\"],\"itSkills\":[{\"clickable\":true,\"id\":\"b87f9211-f56e-4dd2-b7d1-386709c53e47\",\"text\":\"AWS\"},{\"clickable\":true,\"id\":\"26bd0437-457e-11e9-a89e-70106fbef856\",\"text\":\"Data Engineer\"},{\"clickable\":true,\"id\":\"190842bc-457e-11e9-a89e-70106fbef856\",\"text\":\"Sdlc Concepts\"},{\"clickable\":true,\"id\":\"d0658ee0-78a8-4aa0-b5f8-b6ba57dbca26\",\"text\":\"Azure Devops\"}],\"skills\":\"\",\"qualifications\":\"\",\"companyProfile\":\"\",\"recruiter\":\"\",\"kiwiJobId\":\"*********\",\"postedAt\":1744175864000,\"recruiterJobsCount\":\"\",\"recruiterName\":\"HR \",\"company\":{\"country\":\"India\",\"kiwiCompanyId\":1059144,\"city\":\"Bangalore\",\"employerType\":\"company\",\"packagePurchased\":\"**********\",\"score\":1,\"createdAt\":*************,\"kiwiCorpId\":\"1060441\",\"companyId\":1059144,\"keepConfidential\":0,\"phone\":\"**********\",\"isGptw\":0,\"industries\":[null],\"name\":\"Heptarc Technology Solutions Private Limited\",\"logo\":\"https://media.monsterindia.com/logos/xeft_heptarcinx/jdlogo.gif\",\"location\":{\"country\":\"India\",\"city\":\"Bangalore\"},\"maxLoginAccount\":0,\"id\":\"1059144\",\"kiwiXcode\":\"xeft_heptarcinx\",\"email\":\"**********\",\"updatedAt\":*************},\"keySellingPoint1\":\"\",\"keySellingPoint2\":\"\",\"keySellingPoint3\":\"\",\"keySellingPoint4\":\"\",\"keySellingPoint5\":\"\",\"closedAt\":*************,\"employmentTypes\":[\"Full time\"],\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},\"isLoggedIn\":false,\"isMobile\":false,\"pageName\":\"JobDetailPage\",\"host\":\"www.foundit.in\",\"siteProps\":\"$25\",\"showJDLoader\":false,\"abTestingTrackingData\":{\"apiId\":\"V1\",\"pageType\":\"JobDetailPage\",\"data\":{\"jobRank\":1,\"jobId\":\"********\",\"resultCount\":1,\"query\":{\"pageNo\":1,\"pageSize\":1}}}}]]}],[\"$\",\"$L36\",null,{\"isLoggedIn\":false}]]}]\n"])
        </script>
        <script>
            self.__next_f.push([1, "37:I[9621,[\"358\",\"static/chunks/app/(Engagement)/job/%5B...slug%5D/page-8ded5f783839f2d3.js\"],\"default\"]\n3a:I[3527,[\"358\",\"static/chunks/app/(Engagement)/job/%5B...slug%5D/page-8ded5f783839f2d3.js\"],\"default\"]\n3b:I[7453,[\"358\",\"static/chunks/app/(Engagement)/job/%5B...slug%5D/page-8ded5f783839f2d3.js\"],\"default\"]\n39:{\"className\":\"h-4 w-px rotate-[16deg] bg-content-tertiary text-base font-normal\"}\n38:[\"$\",\"span\",null,\"$39\"]\n"])
        </script>
        <script>
            self.__next_f.push([1, "1f:[null,[\"$\",\"$18\",null,{\"fallback\":null,\"children\":[[\"$\",\"$L19\",null,{\"moduleIds\":[\"app/components/jobDetail/SeoSearch/SeoSearch.tsx -\u003e @/app/components/jobDetail/PeopleAlsoConsidered/PeopleAlsoConsidered\"]}],[\"$\",\"$L37\",null,{\"peopleConsideredCacheData\":{\"Delhi\":[],\"Bengaluru / Bangalore\":[],\"Noida\":[],\"Mumbai\":[],\"Hyderabad / Secunderabad Telangana\":[]},\"host\":\"www.foundit.in\",\"isMobile\":false}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"$18\",null,{\"fallback\":null,\"children\":[[\"$\",\"$L19\",null,{\"moduleIds\":[\"app/components/jobDetail/SeoSearch/SeoSearch.tsx -\u003e @/app/components/jobDetail/LastUpdated/LastUpdated\"]}],[\"$\",\"div\",null,{\"className\":\" bg-surface-primary-normal p-4 !pb-2 text-xs  font-normal text-content-tertiary\",\"children\":\"Last Updated: 24-07-2025 09:23:30 AM\"}]]}],[\"$\",\"$18\",null,{\"fallback\":null,\"children\":[[\"$\",\"$L19\",null,{\"moduleIds\":[\"app/components/jobDetail/SeoSearch/SeoSearch.tsx -\u003e @/app/components/jobDetail/Breadcrumbs/BreadCrumbs\"]}],[\"$\",\"div\",null,{\"className\":\"flex items-center justify-start gap-1 bg-surface-primary-normal px-4 text-content-tertiary\",\"children\":[[\"$\",\"span\",null,{\"className\":\" cursor-pointer text-xs font-normal text-brand-primary\",\"children\":[\"$\",\"a\",null,{\"href\":\"/\",\"target\":\"_self\",\"style\":{\"color\":\"#6e00be !important\"},\"children\":\"Home\"}]}],[\"$\",\"span\",null,{\"className\":\"h-4 w-px rotate-[16deg] bg-content-tertiary text-base font-normal\"}],[\"$\",\"a\",null,{\"href\":\"/search/jobs-in-hyderabad-secunderabad-telangana\",\"target\":\"_self\",\"className\":\"cursor-pointer whitespace-nowrap text-xs font-normal text-brand-primary\",\"style\":{\"color\":\"#6e00be !important\"},\"children\":\" Jobs in Hyderabad / Secunderabad, Telangana  \"}],\"$38\",[\"$\",\"span\",null,{\"className\":\"truncate text-xs font-normal capitalize text-content-tertiary\",\"children\":\"Python Developer\"}]]}]]}],[\"$\",\"$18\",null,{\"fallback\":null,\"children\":[[\"$\",\"$L19\",null,{\"moduleIds\":[\"app/components/jobDetail/SeoSearch/SeoSearch.tsx -\u003e @/app/components/jobDetail/ReleatedSearch/ReleatedSearch\"]}],[\"$\",\"$L3a\",null,{\"relatedSearchCacheData\":{\"Hyderabad / Secunderabad\":[],\"Telangana\":[]}}]]}]]}],null,null,[\"$\",\"$18\",null,{\"fallback\":null,\"children\":[[\"$\",\"$L19\",null,{\"moduleIds\":[\"app/components/jobDetail/SeoSearch/SeoSearch.tsx -\u003e @/app/components/common/JobAccordion/JobsAccordion\"]}],[\"$\",\"$L3b\",null,{\"jobAccordionData\":[{\"id\":75,\"name\":\"Jobs by Skill - IT\",\"children\":[{\"id\":76,\"name\":\"Biotechnology Jobs\",\"parent_id\":75},{\"id\":77,\"name\":\"Digital Marketing Jobs\",\"parent_id\":75},{\"id\":78,\"name\":\"Graphic Design Jobs\",\"parent_id\":75},{\"id\":79,\"name\":\"Networking Jobs\",\"parent_id\":75},{\"id\":80,\"name\":\"Oracle Jobs\",\"parent_id\":75},{\"id\":82,\"name\":\"SEO Jobs\",\"parent_id\":75},{\"id\":83,\"name\":\"Software Testing Jobs\",\"parent_id\":75},{\"id\":84,\"name\":\"Sql Jobs\",\"parent_id\":75},{\"id\":85,\"name\":\"Web Design Jobs\",\"parent_id\":75},{\"id\":81,\"name\":\"PHP Jobs\",\"parent_id\":75}]},{\"id\":86,\"name\":\"Jobs by Skill - Non IT\",\"children\":[{\"id\":87,\"name\":\"Accounting Jobs\",\"parent_id\":86},{\"id\":88,\"name\":\"BPO Jobs\",\"parent_id\":86},{\"id\":89,\"name\":\"Call Center Jobs\",\"parent_id\":86},{\"id\":90,\"name\":\"Civil Engineering Jobs\",\"parent_id\":86},{\"id\":91,\"name\":\"Content Writing Jobs\",\"parent_id\":86},{\"id\":92,\"name\":\"Electrical Engineering Jobs\",\"parent_id\":86},{\"id\":93,\"name\":\"Event Management Jobs\",\"parent_id\":86},{\"id\":94,\"name\":\"Hotel Management Jobs\",\"parent_id\":86},{\"id\":95,\"name\":\"HR Jobs\",\"parent_id\":86},{\"id\":96,\"name\":\"Sales Jobs\",\"parent_id\":86}]},{\"id\":108,\"name\":\"International Jobs\",\"children\":[{\"id\":109,\"name\":\"Jobs in Gulf\",\"parent_id\":108},{\"id\":110,\"name\":\"Jobs in Singapore\",\"parent_id\":108},{\"id\":111,\"name\":\"Jobs in Malaysia\",\"parent_id\":108},{\"id\":112,\"name\":\"Jobs in Philippines\",\"parent_id\":108},{\"id\":113,\"name\":\"Jobs in Hong Kong\",\"parent_id\":108},{\"id\":114,\"name\":\"Jobs in Vietnam\",\"parent_id\":108},{\"id\":115,\"name\":\"Jobs in Indonesia\",\"parent_id\":108},{\"id\":116,\"name\":\"Jobs in Thailand\",\"parent_id\":108},{\"id\":117,\"name\":\"Jobs in Dubai\",\"parent_id\":108},{\"id\":118,\"name\":\"Jobs in UAE\",\"parent_id\":108}]}]}]]}]]\n"])
        </script>
        <script>
            self.__next_f.push([1, "3c:I[6585,[\"358\",\"static/chunks/app/(Engagement)/job/%5B...slug%5D/page-8ded5f783839f2d3.js\"],\"default\"]\n3d:T4f1,{\"stopConsentAutoUpdate\":\"false\",\"showUserEngagement\":\"true\",\"heroSectionSubHeading\":\"openings delivered perfectly\",\"resumeParserSwitch\":\"false\",\"syndicatedQuickApplyEnabled\":\"true\",\"bulkApplyMultiSelect\":\"15\",\"oldLoginCheck\":\"true\",\"aspireSwitch\":\"false\",\"showUploadViaGoogleDrive\":\"false\",\"heroSectionHeading\":\"Over 7,00,000+\",\"gaAccountId\":\"UA-********-1\",\"hideCsCartIcon\":\"false\",\"triumphEventId\":\"2\",\"showAppRegistrationPopup\":\"true\",\"recaptchaV2Switch\":\"true\",\"srpBrandBooster\":\"false\",\"dfpSwitch\":\"true\",\"parseNewResume\":\"true\",\"parseResumeSwitchDesktop\":\"false\",\"hideExperienceTypePopup\":\"true\",\"cleverTapProjectId\":\"6K9-ZK8-ZZ6Z\",\"showLinkedInConsent\":\"true\",\"copyrightYear\":\"2020\",\"showDesktopDropboxV2\":\"false\",\"hideBellNotification\":\"false\",\"showPassiveJobs\":\"false\",\"triumphEndDate\":\"14-10-2024\",\"showAspireEnrollPopup\":\"false\",\"jobsCount\":\"Over 8,00,000\",\"showTriumphEvent\":\"true\",\"transparentLogoUrl\":\"https://media.foundit.in/trex/public/default/images/founditTransparentLogo.json\",\"showPromotionsBox\":\"false\",\"parseResumePhoenix\":\"true\",\"showAppLoginPopup\":\"false\",\"logoUrl\":\"https://media.foundit.in/trex/public/default/images/founditLogo.json\",\"resumeParseTime\":\"12000\",\"showAspireElement\":\"false\",\"socialLoginOn\":\"true\",\"newProfileFlow\":\"false\"}"])
        </script>
        <script>
            self.__next_f.push([1, "1e:[\"$\",\"div\",null,{\"id\":\"jdRelatedJobs\",\"className\":\" ml-4 mt-4 flex flex-col gap-2 overflow-hidden md:!ml-0 md:!mt-2 md:!overflow-visible\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-sm font-bold md:!text-base md:!font-semibold\",\"children\":\"Similar Jobs\"}],[\"$\",\"a\",null,{\"href\":\"/home/<USER>/********\",\"children\":[\"$\",\"button\",null,{\"type\":\"button\",\"className\":\"inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 border bg-white border-transparent hover:bg-primary-400 hover:border-primary-400 flex items-center gap-1 text-sm font-normal text-brand-primary\",\"disabled\":false,\"id\":\"viewAllButton\",\"children\":[\"\",\"View all\",[\"$\",\"svg\",null,{\"viewBox\":\"0 0 17 16\",\"fill\":\"currentColor\",\"className\":\"w-4\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"d\":\"M9.48 3.65c.2-.2.51-.2.7 0l4 4c.******* 0 .7l-4 4a.5.5 0 0 1-.7-.7l3.15-3.15H3.17a.5.5 0 0 1 0-1h9.46L9.48 4.35a.5.5 0 0 1 0-.7Z\",\"clipRule\":\"evenodd\"}]}],false]}]}]]}],[\"$\",\"div\",null,{\"className\":\"md:max-w-[879px]\",\"children\":[\"$\",\"$L3c\",null,{\"siteProps\":{\"site\":\"foundit\",\"tenant\":\"msite\",\"category\":\"Home mSite\",\"lang\":null,\"categoryId\":321,\"tenantId\":5,\"kiwiChannel\":null,\"kiwiSubChannel\":null,\"domain\":\"foundit.in\",\"country\":\"india\",\"currency\":\"$27\",\"properties\":\"$3d\",\"festivePropImage\":null,\"festivePropBodyCss\":null,\"festivePropHeaderCss\":null,\"festivePropFooterCss\":null,\"context\":\"rexmonster\",\"favicon\":\"\",\"underMaintainance\":0,\"siteContextProperties\":\"$29\",\"contextSiteProperties\":\"$2a\",\"mediaUrl\":\"media.foundit.in\",\"msuidCookie\":null,\"propertiesMap\":\"$35\",\"mobile\":null,\"siteContext\":\"rexmonster\",\"host\":\"www.foundit.in\",\"jobId\":\"********\"},\"isMobile\":false,\"siteContext\":\"rexmonster\",\"jobDataArray\":[{\"id\":\"35018820\",\"jobId\":35018820,\"title\":\"Python Developer\",\"company\":{\"companyId\":1111284,\"name\":\"Achnet\"},\"locations\":[{\"city\":\"Chennai\",\"country\":\"India\",\"state\":\"Tamil Nadu\",\"latLon\":\"13.082680,80.270718\",\"uuid\":\"b2b9a668-5339-4ea8-8b02-2f7e1a582556\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":44}],\"minimumExperience\":{\"years\":5},\"maximumExperience\":{\"years\":7},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":500000,\"absoluteMonthlyValue\":41667},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":700000,\"absoluteMonthlyValue\":58333},\"updatedAt\":1749704995000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"b87f9211-f56e-4dd2-b7d1-386709c53e47\",\"text\":\"AWS\"},{\"id\":\"80a1ee23-8b82-49d3-89ed-69c10d399ddd\",\"text\":\"Azure\"},{\"id\":\"1ba65d0c-457e-11e9-a89e-70106fbef856\",\"text\":\"Gcp\"},{\"id\":\"8997ffdb-8967-49ed-8b2b-df6a17f05b48\",\"text\":\"Java\"},{\"id\":\"e080169f-056f-46af-84fb-0dc7e16ebe6a\",\"text\":\"Agile\"},{\"id\":\"99871625-b2b9-4bfa-b258-8726f56629ed\",\"text\":\"Scrum\"},{\"id\":\"1d8f08bb-457e-11e9-a89e-70106fbef856\",\"text\":\"No Sql\"},{\"id\":\"eb81df54-a693-4ef7-b44f-ded7e12e1d7c\",\"text\":\"Python\"}],\"companyId\":1111284,\"questionnaire\":false,\"companyName\":\"Achnet\",\"currencyCode\":\"INR\",\"hideSalary\":1,\"hideCompanyName\":0,\"jobSource\":\"ORGANIC\",\"freshness\":1749704995000,\"earlyApplicant\":false,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":true,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},{\"id\":\"35385759\",\"jobId\":35385759,\"title\":\"Python Fullstack Developer\",\"company\":{\"companyId\":1112135,\"name\":\"Value Innovation Labs\"},\"locations\":[{\"city\":\"Hyderabad / Secunderabad, Telangana\",\"country\":\"India\",\"state\":\"Telangana\",\"latLon\":\"17.439930,78.498274\",\"uuid\":\"72da2468-1cbc-4a9a-863a-89e2cfd6502f\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":40}],\"minimumExperience\":{\"years\":5},\"maximumExperience\":{\"years\":10},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":500000,\"absoluteMonthlyValue\":41667},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":1000000,\"absoluteMonthlyValue\":83333},\"updatedAt\":1750759587000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"eb81df54-a693-4ef7-b44f-ded7e12e1d7c\",\"text\":\"Python\"},{\"id\":\"c3fb2149-1744-4226-9795-d7f45786f1c8\",\"text\":\"Django\"},{\"id\":\"18ea8cec-457e-11e9-a89e-70106fbef856\",\"text\":\"React\"},{\"id\":\"268d75ea-457e-11e9-a89e-70106fbef856\",\"text\":\"Restful Apis\"},{\"id\":\"080445eb-0657-4e20-a39c-efe0c7216f78\",\"text\":\"Angular\"}],\"skills\":[{\"text\":\"CI/CD Pipelines\"}],\"companyId\":1112135,\"questionnaire\":false,\"companyName\":\"Value Innovation Labs\",\"currencyCode\":\"INR\",\"hideSalary\":1,\"hideCompanyName\":0,\"jobSource\":\"ORGANIC\",\"freshness\":1750759587000,\"earlyApplicant\":false,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":true,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},{\"id\":\"35051145\",\"jobId\":35051145,\"title\":\"Senior Python Backend Developer - SMTS\",\"company\":{\"companyId\":1111276,\"name\":\"Athenahealth Technology Private Limited\"},\"locations\":[{\"city\":\"Bengaluru / Bangalore\",\"country\":\"India\",\"state\":\"Karnataka\",\"latLon\":\"12.971599,77.594563\",\"uuid\":\"67bfdcae-92cb-4783-bf40-37f639e57710\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":80}],\"minimumExperience\":{\"years\":5},\"maximumExperience\":{\"years\":9},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":500000,\"absoluteMonthlyValue\":41667},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":900000,\"absoluteMonthlyValue\":75000},\"updatedAt\":1749727844000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"eb81df54-a693-4ef7-b44f-ded7e12e1d7c\",\"text\":\"Python\"},{\"id\":\"4a5d94f6-424c-4df8-adaf-700135c94dab\",\"text\":\"Sql\"},{\"id\":\"f0716685-d097-4fa5-b513-7dd496ecf039\",\"text\":\"Hadoop\"},{\"id\":\"b87f9211-f56e-4dd2-b7d1-386709c53e47\",\"text\":\"AWS\"},{\"id\":\"1abd728d-457e-11e9-a89e-70106fbef856\",\"text\":\"Postgres\"},{\"id\":\"a567e6d0-dcd4-4b61-8e17-5284cdff9a5d\",\"text\":\"Linux\"},{\"id\":\"18f7359f-457e-11e9-a89e-70106fbef856\",\"text\":\"Data Warehouse\"},{\"id\":\"1c70ac29-457e-11e9-a89e-70106fbef856\",\"text\":\"Oracle Golden Gate\"}],\"skills\":[{\"id\":\"17282115-5453-11e9-a89e-70106fbef856\",\"text\":\"snowflake \"}],\"companyId\":1111276,\"questionnaire\":false,\"companyName\":\"Athenahealth Technology Private Limited\",\"currencyCode\":\"INR\",\"hideSalary\":1,\"hideCompanyName\":0,\"jobSource\":\"ORGANIC\",\"freshness\":1749727844000,\"earlyApplicant\":false,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":true,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},{\"id\":\"35293215\",\"jobId\":35293215,\"title\":\"Python Developer\",\"company\":{\"companyId\":1111301,\"name\":\"Aqilea\"},\"locations\":[{\"city\":\"Delhi\",\"country\":\"India\",\"state\":\"Delhi\",\"latLon\":\"28.704059,77.102490\",\"uuid\":\"b555ca75-7697-462f-a57d-64b42fb98b49\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":11}],\"minimumExperience\":{\"years\":5},\"maximumExperience\":{\"years\":8},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":500000,\"absoluteMonthlyValue\":41667},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":800000,\"absoluteMonthlyValue\":66667},\"updatedAt\":1750060945000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"2b05c81d-25ef-4199-8236-d295e01f4956\",\"text\":\"Data Migration\"},{\"id\":\"5df3dccf-feb6-4870-bc15-35183828aa69\",\"text\":\"Nosql\"},{\"id\":\"c3fb2149-1744-4226-9795-d7f45786f1c8\",\"text\":\"Django\"},{\"id\":\"18ecd0ee-457e-11e9-a89e-70106fbef856\",\"text\":\"Debugging\"},{\"id\":\"e080169f-056f-46af-84fb-0dc7e16ebe6a\",\"text\":\"Agile\"},{\"id\":\"1b335be5-457e-11e9-a89e-70106fbef856\",\"text\":\"Sdk\"},{\"id\":\"b87f9211-f56e-4dd2-b7d1-386709c53e47\",\"text\":\"AWS\"}],\"skills\":[{\"id\":\"64bbbd3e-4a0b-11e9-a89e-70106fbef856\",\"text\":\"Consulting\"},{\"id\":\"1d3e1c4a-77e6-4fd6-abb8-5a76565e339f\",\"text\":\"Monitoring\"}],\"companyId\":1111301,\"questionnaire\":false,\"companyName\":\"Aqilea\",\"currencyCode\":\"INR\",\"hideSalary\":1,\"hideCompanyName\":0,\"jobSource\":\"ORGANIC\",\"freshness\":1750060945000,\"earlyApplicant\":false,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":true,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},{\"id\":\"34971126\",\"jobId\":34971126,\"title\":\"Python Fullstack Developer\",\"company\":{\"companyId\":1104910,\"name\":\"Mount Talent Consulting\"},\"locations\":[{\"city\":\"Chennai\",\"country\":\"India\",\"state\":\"Tamil Nadu\",\"latLon\":\"13.082680,80.270718\",\"uuid\":\"b2b9a668-5339-4ea8-8b02-2f7e1a582556\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":44}],\"minimumExperience\":{\"years\":5},\"maximumExperience\":{\"years\":10},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":500000,\"absoluteMonthlyValue\":41667},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":1000000,\"absoluteMonthlyValue\":83333},\"updatedAt\":1750247852000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"eb81df54-a693-4ef7-b44f-ded7e12e1d7c\",\"text\":\"Python\"},{\"id\":\"c3fb2149-1744-4226-9795-d7f45786f1c8\",\"text\":\"Django\"},{\"id\":\"b87f9211-f56e-4dd2-b7d1-386709c53e47\",\"text\":\"AWS\"},{\"id\":\"18a19f8a-457e-11e9-a89e-70106fbef856\",\"text\":\"Docker\"},{\"id\":\"************************************\",\"text\":\"Sqlalchemy\"}],\"companyId\":1104910,\"questionnaire\":false,\"companyName\":\"Mount Talent Consulting\",\"currencyCode\":\"INR\",\"hideSalary\":1,\"hideCompanyName\":0,\"jobSource\":\"ORGANIC\",\"freshness\":1748502528000,\"earlyApplicant\":false,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":true,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},{\"id\":\"34968287\",\"jobId\":34968287,\"title\":\"Python Developer Role\",\"company\":{\"companyId\":1064471,\"name\":\"Robotics Technologies\",\"logo\":\"https://media.monsterindia.com/logos/xe_roboticsTechinx/jdlogo.gif\"},\"locations\":[{\"city\":\"Hyderabad / Secunderabad, Telangana\",\"country\":\"India\",\"state\":\"Telangana\",\"latLon\":\"17.439930,78.498274\",\"uuid\":\"72da2468-1cbc-4a9a-863a-89e2cfd6502f\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":40},{\"city\":\"Bengaluru / Bangalore\",\"country\":\"India\",\"state\":\"Karnataka\",\"latLon\":\"12.971599,77.594563\",\"uuid\":\"67bfdcae-92cb-4783-bf40-37f639e57710\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":80},{\"city\":\"Chennai\",\"country\":\"India\",\"state\":\"Tamil Nadu\",\"latLon\":\"13.082680,80.270718\",\"uuid\":\"b2b9a668-5339-4ea8-8b02-2f7e1a582556\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":44}],\"minimumExperience\":{\"years\":5},\"maximumExperience\":{\"years\":10},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":400000,\"absoluteMonthlyValue\":33333},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":1800000,\"absoluteMonthlyValue\":150000},\"updatedAt\":1748423873000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"5df3dccf-feb6-4870-bc15-35183828aa69\",\"text\":\"Nosql\"},{\"id\":\"c3fb2149-1744-4226-9795-d7f45786f1c8\",\"text\":\"Django\"},{\"id\":\"201d787d-8c41-4198-88f0-7b22f8fe3349\",\"text\":\"Unit Testing\"},{\"id\":\"18a19f8a-457e-11e9-a89e-70106fbef856\",\"text\":\"Docker\"},{\"id\":\"************************************\",\"text\":\"Flask\"},{\"id\":\"1cb23608-457e-11e9-a89e-70106fbef856\",\"text\":\"Version Control\"},{\"id\":\"268d75ea-457e-11e9-a89e-70106fbef856\",\"text\":\"Restful Apis\"},{\"id\":\"4a5d94f6-424c-4df8-adaf-700135c94dab\",\"text\":\"Sql\"},{\"id\":\"18dea694-457e-11e9-a89e-70106fbef856\",\"text\":\"Microservices\"}],\"skills\":[{\"id\":\"657b372a-0b9f-4a0b-a923-6f4a293d802f\",\"text\":\"Data Analysis\"}],\"companyId\":1064471,\"questionnaire\":false,\"companyName\":\"Robotics Technologies\",\"currencyCode\":\"INR\",\"hideSalary\":0,\"companyLogoUrl\":\"https://media.monsterindia.com/logos/xe_roboticsTechinx/jdlogo.gif\",\"hideCompanyName\":0,\"jobSource\":\"ORGANIC\",\"freshness\":1748423873000,\"earlyApplicant\":false,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":false,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},{\"id\":\"35290331\",\"jobId\":35290331,\"title\":\"Python Sr. Developer\",\"company\":{\"companyId\":1111306,\"name\":\"Shashwath Solution\"},\"locations\":[{\"city\":\"Cochin / Kochi / Ernakulam\",\"country\":\"India\",\"state\":\"Kerala\",\"latLon\":\"9.931233,76.267304\",\"uuid\":\"2d4187c2-12f9-4e00-8828-da7c4aee2b25\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":484}],\"minimumExperience\":{\"years\":4},\"maximumExperience\":{\"years\":9},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":400000,\"absoluteMonthlyValue\":33333},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":900000,\"absoluteMonthlyValue\":75000},\"updatedAt\":1750052685000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"1b19feb0-457e-11e9-a89e-70106fbef856\",\"text\":\"Rabbitmq\"},{\"id\":\"18a19f8a-457e-11e9-a89e-70106fbef856\",\"text\":\"Docker\"},{\"id\":\"************************************\",\"text\":\"Elasticsearch\"},{\"id\":\"1ba65d0c-457e-11e9-a89e-70106fbef856\",\"text\":\"Gcp\"},{\"id\":\"be7cc27e-a574-4f78-bd1e-783cc3df5a29\",\"text\":\"MySQL\"},{\"id\":\"18ce29fe-457e-11e9-a89e-70106fbef856\",\"text\":\"Kafka\"},{\"id\":\"18dea694-457e-11e9-a89e-70106fbef856\",\"text\":\"Microservices\"},{\"id\":\"************************************\",\"text\":\"Flask\"},{\"id\":\"eb81df54-a693-4ef7-b44f-ded7e12e1d7c\",\"text\":\"Python\"}],\"skills\":[{\"text\":\"Fast API\"}],\"companyId\":1111306,\"questionnaire\":false,\"companyName\":\"Shashwath Solution\",\"currencyCode\":\"INR\",\"hideSalary\":1,\"hideCompanyName\":0,\"jobSource\":\"ORGANIC\",\"freshness\":1750052685000,\"earlyApplicant\":false,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":true,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},{\"id\":\"35450038\",\"jobId\":35450038,\"title\":\"Python Full Stack Developer - Django/Flask/FastAPI\",\"company\":{\"companyId\":1116827,\"name\":\"Qcentrio\"},\"locations\":[{\"city\":\"Kolkata\",\"country\":\"India\",\"state\":\"West Bengal\",\"latLon\":\"22.572646,88.363895\",\"uuid\":\"5521ed10-a43e-497d-861c-873ec797108b\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":33}],\"minimumExperience\":{\"years\":4},\"maximumExperience\":{\"years\":8},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":400000,\"absoluteMonthlyValue\":33333},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":800000,\"absoluteMonthlyValue\":66667},\"updatedAt\":1751433630000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"eb81df54-a693-4ef7-b44f-ded7e12e1d7c\",\"text\":\"Python\"},{\"id\":\"c3fb2149-1744-4226-9795-d7f45786f1c8\",\"text\":\"Django\"},{\"id\":\"************************************\",\"text\":\"Flask\"},{\"id\":\"268d75ea-457e-11e9-a89e-70106fbef856\",\"text\":\"Restful Apis\"},{\"id\":\"5a5c2bfc-5452-11e9-a89e-70106fbef856\",\"text\":\"react.js \"},{\"id\":\"e31a977d-0c52-4546-9877-7ab08928dee6\",\"text\":\"HTML\"}],\"companyId\":1116827,\"questionnaire\":false,\"companyName\":\"Qcentrio\",\"currencyCode\":\"INR\",\"hideSalary\":1,\"hideCompanyName\":0,\"jobSource\":\"ORGANIC\",\"freshness\":1751433630000,\"earlyApplicant\":false,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":true,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},{\"id\":\"34961907\",\"jobId\":34961907,\"title\":\"Python Developer\",\"company\":{\"companyId\":1104666,\"name\":\"SRS Business Solutions India Private Limited\"},\"locations\":[{\"city\":\"Bengaluru / Bangalore\",\"country\":\"India\",\"state\":\"Karnataka\",\"latLon\":\"12.971599,77.594563\",\"uuid\":\"67bfdcae-92cb-4783-bf40-37f639e57710\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":80}],\"minimumExperience\":{\"years\":5},\"maximumExperience\":{\"years\":10},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":800000,\"absoluteMonthlyValue\":66667},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":1800000,\"absoluteMonthlyValue\":150000},\"updatedAt\":1752051210000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"1ba448fe-3e03-44ed-bb2d-20705caaf29b\",\"text\":\"Qa\"},{\"id\":\"1cc3d9d4-457e-11e9-a89e-70106fbef856\",\"text\":\"Cicd\"},{\"id\":\"eb81df54-a693-4ef7-b44f-ded7e12e1d7c\",\"text\":\"Python\"},{\"id\":\"4a5d94f6-424c-4df8-adaf-700135c94dab\",\"text\":\"Sql\"}],\"skills\":[{\"id\":\"a2d748c7-8001-4892-b40f-0e3658aeb1d0\",\"text\":\"Airflow\"},{\"id\":\"17282115-5453-11e9-a89e-70106fbef856\",\"text\":\"snowflake \"}],\"companyId\":1104666,\"questionnaire\":false,\"companyName\":\"SRS Business Solutions India Private Limited\",\"currencyCode\":\"INR\",\"hideSalary\":1,\"hideCompanyName\":0,\"freshness\":1748252338000,\"earlyApplicant\":false,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":true,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},{\"id\":\"35446895\",\"jobId\":35446895,\"title\":\"Python Developer - CAD Applications\",\"company\":{\"companyId\":1116980,\"name\":\"Maimsd Technology\"},\"locations\":[{\"city\":\"Bengaluru / Bangalore\",\"country\":\"India\",\"state\":\"Karnataka\",\"latLon\":\"12.971599,77.594563\",\"uuid\":\"67bfdcae-92cb-4783-bf40-37f639e57710\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":80}],\"minimumExperience\":{\"years\":5},\"maximumExperience\":{\"years\":10},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":500000,\"absoluteMonthlyValue\":41667},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":1000000,\"absoluteMonthlyValue\":83333},\"updatedAt\":1751363035000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"7924e252-67be-4efb-9389-7a296212eed8\",\"text\":\"Software Development\"},{\"id\":\"eb81df54-a693-4ef7-b44f-ded7e12e1d7c\",\"text\":\"Python\"},{\"id\":\"25e35126-457e-11e9-a89e-70106fbef856\",\"text\":\"Oop Concepts\"},{\"id\":\"fcd6a06d-6336-4e5e-86ed-035187b98e0c\",\"text\":\"Unix/Linux\"}],\"skills\":[{\"id\":\"64539387-4a0b-11e9-a89e-70106fbef856\",\"text\":\"Customer Support\"},{\"id\":\"6deafe86-5462-11e9-a89e-70106fbef856\",\"text\":\"development tools\"}],\"companyId\":1116980,\"questionnaire\":false,\"companyName\":\"Maimsd Technology\",\"currencyCode\":\"INR\",\"hideSalary\":1,\"hideCompanyName\":0,\"jobSource\":\"ORGANIC\",\"freshness\":1751363035000,\"earlyApplicant\":false,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":true,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},{\"id\":\"33882522\",\"jobId\":33882522,\"title\":\"Python API developer - Bengaluru/Hyderabad\",\"company\":{\"companyId\":2433,\"name\":\"Tech Mahindra Limited\",\"logo\":\"https://media.monsterindia.com/logos/xnipunainx/jdlogo.gif\"},\"locations\":[{\"city\":\"Hyderabad / Secunderabad, Telangana\",\"country\":\"India\",\"state\":\"Telangana\",\"latLon\":\"17.439930,78.498274\",\"uuid\":\"72da2468-1cbc-4a9a-863a-89e2cfd6502f\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":40},{\"city\":\"Bengaluru / Bangalore\",\"country\":\"India\",\"state\":\"Karnataka\",\"latLon\":\"12.971599,77.594563\",\"uuid\":\"67bfdcae-92cb-4783-bf40-37f639e57710\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":80}],\"minimumExperience\":{\"years\":4},\"maximumExperience\":{\"years\":9},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":300000,\"absoluteMonthlyValue\":25000},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":600000,\"absoluteMonthlyValue\":50000},\"updatedAt\":1750741358000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"8997ffdb-8967-49ed-8b2b-df6a17f05b48\",\"text\":\"Java\"},{\"id\":\"eb81df54-a693-4ef7-b44f-ded7e12e1d7c\",\"text\":\"Python\"},{\"id\":\"80a1ee23-8b82-49d3-89ed-69c10d399ddd\",\"text\":\"Azure\"}],\"skills\":[{\"text\":\"Python API\"}],\"companyId\":2433,\"questionnaire\":false,\"companyName\":\"Tech Mahindra Limited\",\"currencyCode\":\"INR\",\"hideSalary\":1,\"companyLogoUrl\":\"https://media.monsterindia.com/logos/xnipunainx/jdlogo.gif\",\"hideCompanyName\":0,\"jobSource\":\"ORGANIC\",\"freshness\":1750741358000,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":true,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},{\"id\":\"35339651\",\"jobId\":35339651,\"title\":\"SQL Developer (Python/ADF)\",\"company\":{\"companyId\":1101699,\"name\":\"Leuwint Technologies\"},\"locations\":[{\"city\":\"Gurgaon / Gurugram\",\"country\":\"India\",\"state\":\"Haryana\",\"latLon\":\"28.459497,77.026638\",\"uuid\":\"19279f4d-4436-4887-839c-bdd80d13d3fb\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":124}],\"minimumExperience\":{\"years\":5},\"maximumExperience\":{\"years\":9},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":500000,\"absoluteMonthlyValue\":41667},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":900000,\"absoluteMonthlyValue\":75000},\"updatedAt\":1750156687000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"8a5b596b-1b07-4d1d-a438-28a9d6bb5ff8\",\"text\":\"T-sql\"},{\"id\":\"1ad57992-457e-11e9-a89e-70106fbef856\",\"text\":\"Adf\"},{\"id\":\"b18d76d5-e8eb-4974-95b9-3c66fd291897\",\"text\":\"Cloud\"},{\"id\":\"eef3d259-78c3-44c4-85b1-23a69f52ff4b\",\"text\":\"SQL Server\"},{\"id\":\"b87f9211-f56e-4dd2-b7d1-386709c53e47\",\"text\":\"AWS\"},{\"id\":\"93a92d1e-b021-46e4-b100-ccb0444420d4\",\"text\":\"Ssrs\"}],\"companyId\":1101699,\"questionnaire\":false,\"companyName\":\"Leuwint Technologies\",\"currencyCode\":\"INR\",\"hideSalary\":1,\"hideCompanyName\":0,\"jobSource\":\"ORGANIC\",\"freshness\":1750156687000,\"earlyApplicant\":false,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":true,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"},{\"id\":\"35385338\",\"jobId\":35385338,\"title\":\"Full Stack Developer (Python)\",\"company\":{\"companyId\":1112128,\"name\":\"Ztek Consulting\"},\"locations\":[{\"city\":\"Delhi\",\"country\":\"India\",\"state\":\"Delhi\",\"latLon\":\"28.704059,77.102490\",\"uuid\":\"b555ca75-7697-462f-a57d-64b42fb98b49\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":11},{\"city\":\"Kolkata\",\"country\":\"India\",\"state\":\"West Bengal\",\"latLon\":\"22.572646,88.363895\",\"uuid\":\"5521ed10-a43e-497d-861c-873ec797108b\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":33},{\"city\":\"Mumbai\",\"country\":\"India\",\"state\":\"Maharashtra\",\"latLon\":\"19.075984,72.877656\",\"uuid\":\"db3e442b-e262-4869-bf5e-29c815f41b58\",\"isoCode\":\"IN\",\"isdCode\":91,\"stdCode\":22}],\"minimumExperience\":{\"years\":4},\"maximumExperience\":{\"years\":10},\"minimumSalary\":{\"currency\":\"INR\",\"absoluteValue\":400000,\"absoluteMonthlyValue\":33333},\"maximumSalary\":{\"currency\":\"INR\",\"absoluteValue\":1000000,\"absoluteMonthlyValue\":83333},\"updatedAt\":1750753270000,\"jobTypes\":[\"Permanent Job\"],\"employmentTypes\":[\"Full time\"],\"itSkills\":[{\"id\":\"254850b1-457e-11e9-a89e-70106fbef856\",\"text\":\"Python Django\"},{\"id\":\"18a6563c-457e-11e9-a89e-70106fbef856\",\"text\":\"Etl\"},{\"id\":\"18d47f8e-457e-11e9-a89e-70106fbef856\",\"text\":\"Rest Api\"},{\"id\":\"b87f9211-f56e-4dd2-b7d1-386709c53e47\",\"text\":\"AWS\"},{\"id\":\"18ea8cec-457e-11e9-a89e-70106fbef856\",\"text\":\"React\"},{\"id\":\"25f69084-457e-11e9-a89e-70106fbef856\",\"text\":\"Backend Development\"}],\"skills\":[{\"text\":\"Full Stack Developement\"}],\"companyId\":1112128,\"questionnaire\":false,\"companyName\":\"Ztek Consulting\",\"currencyCode\":\"INR\",\"hideSalary\":1,\"hideCompanyName\":0,\"jobSource\":\"ORGANIC\",\"freshness\":1750753271000,\"earlyApplicant\":false,\"isMicrosite\":0,\"isGptwCompany\":0,\"jobSalaryConfidential\":true,\"quickApplyJob\":1,\"jobClassification\":\"ORGANIC\"}],\"isLoggedIn\":false}]}]]}]\n"])
        </script>
        <link rel="preload" as="image" href="https://media.monsterindia.com/logos/xe_roboticsTechinx/jdlogo.gif"/>
        <link rel="preload" as="image" href="https://media.monsterindia.com/logos/xnipunainx/jdlogo.gif"/>
        <div hidden id="S:0">
            <div id="jdRelatedJobs" class=" ml-4 mt-4 flex flex-col gap-2 overflow-hidden md:!ml-0 md:!mt-2 md:!overflow-visible">
                <div class="flex items-center justify-between">
                    <h2 class="text-sm font-bold md:!text-base md:!font-semibold">Similar Jobs</h2>
                    <a href="/home/<USER>/********">
                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 border bg-white border-transparent hover:bg-primary-400 hover:border-primary-400 flex items-center gap-1 text-sm font-normal text-brand-primary" id="viewAllButton">
                            View all
                            <svg viewBox="0 0 17 16" fill="currentColor" class="w-4">
                                <path fill-rule="evenodd" d="M9.48 3.65c.2-.2.51-.2.7 0l4 4c.******* 0 .7l-4 4a.5.5 0 0 1-.7-.7l3.15-3.15H3.17a.5.5 0 0 1 0-1h9.46L9.48 4.35a.5.5 0 0 1 0-.7Z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </a>
                </div>
                <div class="md:max-w-[879px]">
                    <div class="slick-slider slick-initialized" dir="ltr">
                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-4 py-3 text-sm font-semibold text-grey-300 bg-white border-transparent cursor-not-allowed slick-arrow slick-prev slick-disabled !bg-surface-primary-normal -left-5 z-[9] size-8 rounded-full border border-solid !border-border-moderate !p-0 md:inline-flex lg:inline-flex" disabled="" style="box-shadow:0px 0px 10px 0px #00000040">
                            <svg fill="#d2d1d6" viewBox="0 0 24 24" height="21" width="21" class="absolute rotate-90">
                                <path fill-rule="evenodd" d="M5.47 9.47c.3-.3.77-.3 1.06 0L12 14.94l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6 6c-.3.3-.77.3-1.06 0l-6-6a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <div class="slick-list">
                            <div class="slick-track" style="width:650%;left:0%">
                                <div data-index="0" class="slick-slide slick-active slick-current" tabindex="-1" aria-hidden="false" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="flex size-11 min-w-11 items-center justify-center self-center rounded bg-decorative-surface-03">
                                                        <svg viewBox="0 0 15 14" width="24" height="24" fill="#5A0A40">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.61321 0.5H6.05379C6.40989 0.499993 6.70712 0.499987 6.94994 0.519827C7.2032 0.540519 7.44097 0.585259 7.66581 0.699822C8.01078 0.87559 8.29124 1.15605 8.46701 1.50102C8.58157 1.72586 8.62631 1.96363 8.647 2.21689C8.66684 2.45971 8.66684 2.75695 8.66683 3.11306V5.83333H11.3871C11.7432 5.83333 12.0405 5.83332 12.2833 5.85316C12.5365 5.87385 12.7743 5.91859 12.9991 6.03316C13.3441 6.20892 13.6246 6.48939 13.8003 6.83435C13.9149 7.05919 13.9596 7.29696 13.9803 7.55022C14.0002 7.79304 14.0002 8.09027 14.0002 8.44637V12.5H14.1668C14.443 12.5 14.6668 12.7239 14.6668 13C14.6668 13.2761 14.443 13.5 14.1668 13.5H0.833496C0.557354 13.5 0.333496 13.2761 0.333496 13C0.333496 12.7239 0.557354 12.5 0.833496 12.5H1.00016L1.00016 3.11304C1.00016 2.75694 1.00015 2.45971 1.01999 2.21689C1.04068 1.96363 1.08542 1.72586 1.19998 1.50102C1.37575 1.15605 1.65622 0.87559 2.00118 0.699822C2.22602 0.585259 2.46379 0.540519 2.71705 0.519827C2.95987 0.499987 3.2571 0.499993 3.61321 0.5ZM2.00016 12.5H7.66683V3.13333C7.66683 2.75172 7.66644 2.49557 7.65032 2.29832C7.63468 2.1069 7.60676 2.01538 7.576 1.95501C7.49611 1.79821 7.36862 1.67072 7.21182 1.59083C7.15145 1.56007 7.05993 1.53214 6.86851 1.51651C6.67126 1.50039 6.41511 1.5 6.0335 1.5H3.6335C3.25188 1.5 2.99573 1.50039 2.79848 1.51651C2.60707 1.53214 2.51554 1.56007 2.45517 1.59083C2.29837 1.67072 2.17089 1.79821 2.09099 1.95501C2.06023 2.01538 2.03231 2.1069 2.01667 2.29832C2.00055 2.49557 2.00016 2.75172 2.00016 3.13333V12.5ZM8.66683 6.83333V12.5H13.0002V8.46667C13.0002 8.08505 12.9998 7.82891 12.9837 7.63165C12.968 7.44024 12.9401 7.34872 12.9093 7.28834C12.8294 7.13154 12.702 7.00406 12.5452 6.92416C12.4848 6.8934 12.3933 6.86548 12.2018 6.84984C12.0046 6.83372 11.7484 6.83333 11.3668 6.83333H8.66683ZM3.3335 3.66667C3.3335 3.39052 3.55735 3.16667 3.8335 3.16667H5.8335C6.10964 3.16667 6.3335 3.39052 6.3335 3.66667C6.3335 3.94281 6.10964 4.16667 5.8335 4.16667H3.8335C3.55735 4.16667 3.3335 3.94281 3.3335 3.66667ZM3.3335 6.33333C3.3335 6.05719 3.55735 5.83333 3.8335 5.83333H5.8335C6.10964 5.83333 6.3335 6.05719 6.3335 6.33333C6.3335 6.60948 6.10964 6.83333 5.8335 6.83333H3.8335C3.55735 6.83333 3.3335 6.60948 3.3335 6.33333ZM3.3335 9C3.3335 8.72386 3.55735 8.5 3.8335 8.5H5.8335C6.10964 8.5 6.3335 8.72386 6.3335 9C6.3335 9.27614 6.10964 9.5 5.8335 9.5H3.8335C3.55735 9.5 3.3335 9.27614 3.3335 9Z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">Python Developer</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">Achnet</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>5-7 yrs</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Chennai</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-index="1" class="slick-slide slick-active" tabindex="-1" aria-hidden="false" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="flex size-11 min-w-11 items-center justify-center self-center rounded bg-decorative-surface-03">
                                                        <svg viewBox="0 0 15 14" width="24" height="24" fill="#5A0A40">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.61321 0.5H6.05379C6.40989 0.499993 6.70712 0.499987 6.94994 0.519827C7.2032 0.540519 7.44097 0.585259 7.66581 0.699822C8.01078 0.87559 8.29124 1.15605 8.46701 1.50102C8.58157 1.72586 8.62631 1.96363 8.647 2.21689C8.66684 2.45971 8.66684 2.75695 8.66683 3.11306V5.83333H11.3871C11.7432 5.83333 12.0405 5.83332 12.2833 5.85316C12.5365 5.87385 12.7743 5.91859 12.9991 6.03316C13.3441 6.20892 13.6246 6.48939 13.8003 6.83435C13.9149 7.05919 13.9596 7.29696 13.9803 7.55022C14.0002 7.79304 14.0002 8.09027 14.0002 8.44637V12.5H14.1668C14.443 12.5 14.6668 12.7239 14.6668 13C14.6668 13.2761 14.443 13.5 14.1668 13.5H0.833496C0.557354 13.5 0.333496 13.2761 0.333496 13C0.333496 12.7239 0.557354 12.5 0.833496 12.5H1.00016L1.00016 3.11304C1.00016 2.75694 1.00015 2.45971 1.01999 2.21689C1.04068 1.96363 1.08542 1.72586 1.19998 1.50102C1.37575 1.15605 1.65622 0.87559 2.00118 0.699822C2.22602 0.585259 2.46379 0.540519 2.71705 0.519827C2.95987 0.499987 3.2571 0.499993 3.61321 0.5ZM2.00016 12.5H7.66683V3.13333C7.66683 2.75172 7.66644 2.49557 7.65032 2.29832C7.63468 2.1069 7.60676 2.01538 7.576 1.95501C7.49611 1.79821 7.36862 1.67072 7.21182 1.59083C7.15145 1.56007 7.05993 1.53214 6.86851 1.51651C6.67126 1.50039 6.41511 1.5 6.0335 1.5H3.6335C3.25188 1.5 2.99573 1.50039 2.79848 1.51651C2.60707 1.53214 2.51554 1.56007 2.45517 1.59083C2.29837 1.67072 2.17089 1.79821 2.09099 1.95501C2.06023 2.01538 2.03231 2.1069 2.01667 2.29832C2.00055 2.49557 2.00016 2.75172 2.00016 3.13333V12.5ZM8.66683 6.83333V12.5H13.0002V8.46667C13.0002 8.08505 12.9998 7.82891 12.9837 7.63165C12.968 7.44024 12.9401 7.34872 12.9093 7.28834C12.8294 7.13154 12.702 7.00406 12.5452 6.92416C12.4848 6.8934 12.3933 6.86548 12.2018 6.84984C12.0046 6.83372 11.7484 6.83333 11.3668 6.83333H8.66683ZM3.3335 3.66667C3.3335 3.39052 3.55735 3.16667 3.8335 3.16667H5.8335C6.10964 3.16667 6.3335 3.39052 6.3335 3.66667C6.3335 3.94281 6.10964 4.16667 5.8335 4.16667H3.8335C3.55735 4.16667 3.3335 3.94281 3.3335 3.66667ZM3.3335 6.33333C3.3335 6.05719 3.55735 5.83333 3.8335 5.83333H5.8335C6.10964 5.83333 6.3335 6.05719 6.3335 6.33333C6.3335 6.60948 6.10964 6.83333 5.8335 6.83333H3.8335C3.55735 6.83333 3.3335 6.60948 3.3335 6.33333ZM3.3335 9C3.3335 8.72386 3.55735 8.5 3.8335 8.5H5.8335C6.10964 8.5 6.3335 8.72386 6.3335 9C6.3335 9.27614 6.10964 9.5 5.8335 9.5H3.8335C3.55735 9.5 3.3335 9.27614 3.3335 9Z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">Python Fullstack Developer</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">Value Innovation Labs</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>5-10 yrs</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Hyderabad</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-index="2" class="slick-slide" tabindex="-1" aria-hidden="true" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="flex size-11 min-w-11 items-center justify-center self-center rounded bg-decorative-surface-03">
                                                        <svg viewBox="0 0 15 14" width="24" height="24" fill="#5A0A40">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.61321 0.5H6.05379C6.40989 0.499993 6.70712 0.499987 6.94994 0.519827C7.2032 0.540519 7.44097 0.585259 7.66581 0.699822C8.01078 0.87559 8.29124 1.15605 8.46701 1.50102C8.58157 1.72586 8.62631 1.96363 8.647 2.21689C8.66684 2.45971 8.66684 2.75695 8.66683 3.11306V5.83333H11.3871C11.7432 5.83333 12.0405 5.83332 12.2833 5.85316C12.5365 5.87385 12.7743 5.91859 12.9991 6.03316C13.3441 6.20892 13.6246 6.48939 13.8003 6.83435C13.9149 7.05919 13.9596 7.29696 13.9803 7.55022C14.0002 7.79304 14.0002 8.09027 14.0002 8.44637V12.5H14.1668C14.443 12.5 14.6668 12.7239 14.6668 13C14.6668 13.2761 14.443 13.5 14.1668 13.5H0.833496C0.557354 13.5 0.333496 13.2761 0.333496 13C0.333496 12.7239 0.557354 12.5 0.833496 12.5H1.00016L1.00016 3.11304C1.00016 2.75694 1.00015 2.45971 1.01999 2.21689C1.04068 1.96363 1.08542 1.72586 1.19998 1.50102C1.37575 1.15605 1.65622 0.87559 2.00118 0.699822C2.22602 0.585259 2.46379 0.540519 2.71705 0.519827C2.95987 0.499987 3.2571 0.499993 3.61321 0.5ZM2.00016 12.5H7.66683V3.13333C7.66683 2.75172 7.66644 2.49557 7.65032 2.29832C7.63468 2.1069 7.60676 2.01538 7.576 1.95501C7.49611 1.79821 7.36862 1.67072 7.21182 1.59083C7.15145 1.56007 7.05993 1.53214 6.86851 1.51651C6.67126 1.50039 6.41511 1.5 6.0335 1.5H3.6335C3.25188 1.5 2.99573 1.50039 2.79848 1.51651C2.60707 1.53214 2.51554 1.56007 2.45517 1.59083C2.29837 1.67072 2.17089 1.79821 2.09099 1.95501C2.06023 2.01538 2.03231 2.1069 2.01667 2.29832C2.00055 2.49557 2.00016 2.75172 2.00016 3.13333V12.5ZM8.66683 6.83333V12.5H13.0002V8.46667C13.0002 8.08505 12.9998 7.82891 12.9837 7.63165C12.968 7.44024 12.9401 7.34872 12.9093 7.28834C12.8294 7.13154 12.702 7.00406 12.5452 6.92416C12.4848 6.8934 12.3933 6.86548 12.2018 6.84984C12.0046 6.83372 11.7484 6.83333 11.3668 6.83333H8.66683ZM3.3335 3.66667C3.3335 3.39052 3.55735 3.16667 3.8335 3.16667H5.8335C6.10964 3.16667 6.3335 3.39052 6.3335 3.66667C6.3335 3.94281 6.10964 4.16667 5.8335 4.16667H3.8335C3.55735 4.16667 3.3335 3.94281 3.3335 3.66667ZM3.3335 6.33333C3.3335 6.05719 3.55735 5.83333 3.8335 5.83333H5.8335C6.10964 5.83333 6.3335 6.05719 6.3335 6.33333C6.3335 6.60948 6.10964 6.83333 5.8335 6.83333H3.8335C3.55735 6.83333 3.3335 6.60948 3.3335 6.33333ZM3.3335 9C3.3335 8.72386 3.55735 8.5 3.8335 8.5H5.8335C6.10964 8.5 6.3335 8.72386 6.3335 9C6.3335 9.27614 6.10964 9.5 5.8335 9.5H3.8335C3.55735 9.5 3.3335 9.27614 3.3335 9Z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">Senior Python Backend Developer - SMTS</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">Athenahealth Technology Private Limited</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>5-9 yrs</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Bengaluru</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-index="3" class="slick-slide" tabindex="-1" aria-hidden="true" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="flex size-11 min-w-11 items-center justify-center self-center rounded bg-decorative-surface-03">
                                                        <svg viewBox="0 0 15 14" width="24" height="24" fill="#5A0A40">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.61321 0.5H6.05379C6.40989 0.499993 6.70712 0.499987 6.94994 0.519827C7.2032 0.540519 7.44097 0.585259 7.66581 0.699822C8.01078 0.87559 8.29124 1.15605 8.46701 1.50102C8.58157 1.72586 8.62631 1.96363 8.647 2.21689C8.66684 2.45971 8.66684 2.75695 8.66683 3.11306V5.83333H11.3871C11.7432 5.83333 12.0405 5.83332 12.2833 5.85316C12.5365 5.87385 12.7743 5.91859 12.9991 6.03316C13.3441 6.20892 13.6246 6.48939 13.8003 6.83435C13.9149 7.05919 13.9596 7.29696 13.9803 7.55022C14.0002 7.79304 14.0002 8.09027 14.0002 8.44637V12.5H14.1668C14.443 12.5 14.6668 12.7239 14.6668 13C14.6668 13.2761 14.443 13.5 14.1668 13.5H0.833496C0.557354 13.5 0.333496 13.2761 0.333496 13C0.333496 12.7239 0.557354 12.5 0.833496 12.5H1.00016L1.00016 3.11304C1.00016 2.75694 1.00015 2.45971 1.01999 2.21689C1.04068 1.96363 1.08542 1.72586 1.19998 1.50102C1.37575 1.15605 1.65622 0.87559 2.00118 0.699822C2.22602 0.585259 2.46379 0.540519 2.71705 0.519827C2.95987 0.499987 3.2571 0.499993 3.61321 0.5ZM2.00016 12.5H7.66683V3.13333C7.66683 2.75172 7.66644 2.49557 7.65032 2.29832C7.63468 2.1069 7.60676 2.01538 7.576 1.95501C7.49611 1.79821 7.36862 1.67072 7.21182 1.59083C7.15145 1.56007 7.05993 1.53214 6.86851 1.51651C6.67126 1.50039 6.41511 1.5 6.0335 1.5H3.6335C3.25188 1.5 2.99573 1.50039 2.79848 1.51651C2.60707 1.53214 2.51554 1.56007 2.45517 1.59083C2.29837 1.67072 2.17089 1.79821 2.09099 1.95501C2.06023 2.01538 2.03231 2.1069 2.01667 2.29832C2.00055 2.49557 2.00016 2.75172 2.00016 3.13333V12.5ZM8.66683 6.83333V12.5H13.0002V8.46667C13.0002 8.08505 12.9998 7.82891 12.9837 7.63165C12.968 7.44024 12.9401 7.34872 12.9093 7.28834C12.8294 7.13154 12.702 7.00406 12.5452 6.92416C12.4848 6.8934 12.3933 6.86548 12.2018 6.84984C12.0046 6.83372 11.7484 6.83333 11.3668 6.83333H8.66683ZM3.3335 3.66667C3.3335 3.39052 3.55735 3.16667 3.8335 3.16667H5.8335C6.10964 3.16667 6.3335 3.39052 6.3335 3.66667C6.3335 3.94281 6.10964 4.16667 5.8335 4.16667H3.8335C3.55735 4.16667 3.3335 3.94281 3.3335 3.66667ZM3.3335 6.33333C3.3335 6.05719 3.55735 5.83333 3.8335 5.83333H5.8335C6.10964 5.83333 6.3335 6.05719 6.3335 6.33333C6.3335 6.60948 6.10964 6.83333 5.8335 6.83333H3.8335C3.55735 6.83333 3.3335 6.60948 3.3335 6.33333ZM3.3335 9C3.3335 8.72386 3.55735 8.5 3.8335 8.5H5.8335C6.10964 8.5 6.3335 8.72386 6.3335 9C6.3335 9.27614 6.10964 9.5 5.8335 9.5H3.8335C3.55735 9.5 3.3335 9.27614 3.3335 9Z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">Python Developer</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">Aqilea</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>5-8 yrs</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Delhi</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-index="4" class="slick-slide" tabindex="-1" aria-hidden="true" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="flex size-11 min-w-11 items-center justify-center self-center rounded bg-decorative-surface-03">
                                                        <svg viewBox="0 0 15 14" width="24" height="24" fill="#5A0A40">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.61321 0.5H6.05379C6.40989 0.499993 6.70712 0.499987 6.94994 0.519827C7.2032 0.540519 7.44097 0.585259 7.66581 0.699822C8.01078 0.87559 8.29124 1.15605 8.46701 1.50102C8.58157 1.72586 8.62631 1.96363 8.647 2.21689C8.66684 2.45971 8.66684 2.75695 8.66683 3.11306V5.83333H11.3871C11.7432 5.83333 12.0405 5.83332 12.2833 5.85316C12.5365 5.87385 12.7743 5.91859 12.9991 6.03316C13.3441 6.20892 13.6246 6.48939 13.8003 6.83435C13.9149 7.05919 13.9596 7.29696 13.9803 7.55022C14.0002 7.79304 14.0002 8.09027 14.0002 8.44637V12.5H14.1668C14.443 12.5 14.6668 12.7239 14.6668 13C14.6668 13.2761 14.443 13.5 14.1668 13.5H0.833496C0.557354 13.5 0.333496 13.2761 0.333496 13C0.333496 12.7239 0.557354 12.5 0.833496 12.5H1.00016L1.00016 3.11304C1.00016 2.75694 1.00015 2.45971 1.01999 2.21689C1.04068 1.96363 1.08542 1.72586 1.19998 1.50102C1.37575 1.15605 1.65622 0.87559 2.00118 0.699822C2.22602 0.585259 2.46379 0.540519 2.71705 0.519827C2.95987 0.499987 3.2571 0.499993 3.61321 0.5ZM2.00016 12.5H7.66683V3.13333C7.66683 2.75172 7.66644 2.49557 7.65032 2.29832C7.63468 2.1069 7.60676 2.01538 7.576 1.95501C7.49611 1.79821 7.36862 1.67072 7.21182 1.59083C7.15145 1.56007 7.05993 1.53214 6.86851 1.51651C6.67126 1.50039 6.41511 1.5 6.0335 1.5H3.6335C3.25188 1.5 2.99573 1.50039 2.79848 1.51651C2.60707 1.53214 2.51554 1.56007 2.45517 1.59083C2.29837 1.67072 2.17089 1.79821 2.09099 1.95501C2.06023 2.01538 2.03231 2.1069 2.01667 2.29832C2.00055 2.49557 2.00016 2.75172 2.00016 3.13333V12.5ZM8.66683 6.83333V12.5H13.0002V8.46667C13.0002 8.08505 12.9998 7.82891 12.9837 7.63165C12.968 7.44024 12.9401 7.34872 12.9093 7.28834C12.8294 7.13154 12.702 7.00406 12.5452 6.92416C12.4848 6.8934 12.3933 6.86548 12.2018 6.84984C12.0046 6.83372 11.7484 6.83333 11.3668 6.83333H8.66683ZM3.3335 3.66667C3.3335 3.39052 3.55735 3.16667 3.8335 3.16667H5.8335C6.10964 3.16667 6.3335 3.39052 6.3335 3.66667C6.3335 3.94281 6.10964 4.16667 5.8335 4.16667H3.8335C3.55735 4.16667 3.3335 3.94281 3.3335 3.66667ZM3.3335 6.33333C3.3335 6.05719 3.55735 5.83333 3.8335 5.83333H5.8335C6.10964 5.83333 6.3335 6.05719 6.3335 6.33333C6.3335 6.60948 6.10964 6.83333 5.8335 6.83333H3.8335C3.55735 6.83333 3.3335 6.60948 3.3335 6.33333ZM3.3335 9C3.3335 8.72386 3.55735 8.5 3.8335 8.5H5.8335C6.10964 8.5 6.3335 8.72386 6.3335 9C6.3335 9.27614 6.10964 9.5 5.8335 9.5H3.8335C3.55735 9.5 3.3335 9.27614 3.3335 9Z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">Python Fullstack Developer</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">Mount Talent Consulting</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>5-10 yrs</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Chennai</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-index="5" class="slick-slide" tabindex="-1" aria-hidden="true" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="border-border-subtle flex size-11 min-w-11 items-center self-start rounded border border-solid p-[1px]">
                                                        <img src="https://media.monsterindia.com/logos/xe_roboticsTechinx/jdlogo.gif" alt="Robotics Technologies" class="w-11"/>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">Python Developer Role</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">Robotics Technologies</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>5-10 yrs</label>
                                                    </div>
                                                    <span class="size-[6px] rounded-full bg-fontColor-content-tertiary"></span>
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 17 16" width="16" height="16" fill="#59566C">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.83333 2.5C3.3731 2.5 3 2.8731 3 3.33333C3 3.79357 3.3731 4.16667 3.83333 4.16667L13.1667 4.16667C14.1792 4.16667 15 4.98748 15 6V12.6667C15 13.6792 14.1792 14.5 13.1667 14.5H3.83333C2.82081 14.5 2 13.6792 2 12.6667V3.33333C2 2.32081 2.82081 1.5 3.83333 1.5H11.8333C12.1095 1.5 12.3333 1.72386 12.3333 2C12.3333 2.27614 12.1095 2.5 11.8333 2.5H3.83333ZM3 4.96676V12.6667C3 13.1269 3.3731 13.5 3.83333 13.5H13.1667C13.6269 13.5 14 13.1269 14 12.6667V6C14 5.53976 13.6269 5.16667 13.1667 5.16667L3.83333 5.16667C3.53326 5.16667 3.25002 5.09457 3 4.96676ZM10.6667 9.33333C10.6667 8.8731 11.0398 8.5 11.5 8.5C11.9602 8.5 12.3333 8.8731 12.3333 9.33333C12.3333 9.79357 11.9602 10.1667 11.5 10.1667C11.0398 10.1667 10.6667 9.79357 10.6667 9.33333Z"></path>
                                                        </svg>
                                                        <label>INR 4 - 18 LPA</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Hyderabad, Bengaluru, Chennai</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-index="6" class="slick-slide" tabindex="-1" aria-hidden="true" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="flex size-11 min-w-11 items-center justify-center self-center rounded bg-decorative-surface-03">
                                                        <svg viewBox="0 0 15 14" width="24" height="24" fill="#5A0A40">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.61321 0.5H6.05379C6.40989 0.499993 6.70712 0.499987 6.94994 0.519827C7.2032 0.540519 7.44097 0.585259 7.66581 0.699822C8.01078 0.87559 8.29124 1.15605 8.46701 1.50102C8.58157 1.72586 8.62631 1.96363 8.647 2.21689C8.66684 2.45971 8.66684 2.75695 8.66683 3.11306V5.83333H11.3871C11.7432 5.83333 12.0405 5.83332 12.2833 5.85316C12.5365 5.87385 12.7743 5.91859 12.9991 6.03316C13.3441 6.20892 13.6246 6.48939 13.8003 6.83435C13.9149 7.05919 13.9596 7.29696 13.9803 7.55022C14.0002 7.79304 14.0002 8.09027 14.0002 8.44637V12.5H14.1668C14.443 12.5 14.6668 12.7239 14.6668 13C14.6668 13.2761 14.443 13.5 14.1668 13.5H0.833496C0.557354 13.5 0.333496 13.2761 0.333496 13C0.333496 12.7239 0.557354 12.5 0.833496 12.5H1.00016L1.00016 3.11304C1.00016 2.75694 1.00015 2.45971 1.01999 2.21689C1.04068 1.96363 1.08542 1.72586 1.19998 1.50102C1.37575 1.15605 1.65622 0.87559 2.00118 0.699822C2.22602 0.585259 2.46379 0.540519 2.71705 0.519827C2.95987 0.499987 3.2571 0.499993 3.61321 0.5ZM2.00016 12.5H7.66683V3.13333C7.66683 2.75172 7.66644 2.49557 7.65032 2.29832C7.63468 2.1069 7.60676 2.01538 7.576 1.95501C7.49611 1.79821 7.36862 1.67072 7.21182 1.59083C7.15145 1.56007 7.05993 1.53214 6.86851 1.51651C6.67126 1.50039 6.41511 1.5 6.0335 1.5H3.6335C3.25188 1.5 2.99573 1.50039 2.79848 1.51651C2.60707 1.53214 2.51554 1.56007 2.45517 1.59083C2.29837 1.67072 2.17089 1.79821 2.09099 1.95501C2.06023 2.01538 2.03231 2.1069 2.01667 2.29832C2.00055 2.49557 2.00016 2.75172 2.00016 3.13333V12.5ZM8.66683 6.83333V12.5H13.0002V8.46667C13.0002 8.08505 12.9998 7.82891 12.9837 7.63165C12.968 7.44024 12.9401 7.34872 12.9093 7.28834C12.8294 7.13154 12.702 7.00406 12.5452 6.92416C12.4848 6.8934 12.3933 6.86548 12.2018 6.84984C12.0046 6.83372 11.7484 6.83333 11.3668 6.83333H8.66683ZM3.3335 3.66667C3.3335 3.39052 3.55735 3.16667 3.8335 3.16667H5.8335C6.10964 3.16667 6.3335 3.39052 6.3335 3.66667C6.3335 3.94281 6.10964 4.16667 5.8335 4.16667H3.8335C3.55735 4.16667 3.3335 3.94281 3.3335 3.66667ZM3.3335 6.33333C3.3335 6.05719 3.55735 5.83333 3.8335 5.83333H5.8335C6.10964 5.83333 6.3335 6.05719 6.3335 6.33333C6.3335 6.60948 6.10964 6.83333 5.8335 6.83333H3.8335C3.55735 6.83333 3.3335 6.60948 3.3335 6.33333ZM3.3335 9C3.3335 8.72386 3.55735 8.5 3.8335 8.5H5.8335C6.10964 8.5 6.3335 8.72386 6.3335 9C6.3335 9.27614 6.10964 9.5 5.8335 9.5H3.8335C3.55735 9.5 3.3335 9.27614 3.3335 9Z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">Python Sr. Developer</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">Shashwath Solution</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>4-9 yrs</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Cochin / Kochi / Ernakulam</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-index="7" class="slick-slide" tabindex="-1" aria-hidden="true" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="flex size-11 min-w-11 items-center justify-center self-center rounded bg-decorative-surface-03">
                                                        <svg viewBox="0 0 15 14" width="24" height="24" fill="#5A0A40">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.61321 0.5H6.05379C6.40989 0.499993 6.70712 0.499987 6.94994 0.519827C7.2032 0.540519 7.44097 0.585259 7.66581 0.699822C8.01078 0.87559 8.29124 1.15605 8.46701 1.50102C8.58157 1.72586 8.62631 1.96363 8.647 2.21689C8.66684 2.45971 8.66684 2.75695 8.66683 3.11306V5.83333H11.3871C11.7432 5.83333 12.0405 5.83332 12.2833 5.85316C12.5365 5.87385 12.7743 5.91859 12.9991 6.03316C13.3441 6.20892 13.6246 6.48939 13.8003 6.83435C13.9149 7.05919 13.9596 7.29696 13.9803 7.55022C14.0002 7.79304 14.0002 8.09027 14.0002 8.44637V12.5H14.1668C14.443 12.5 14.6668 12.7239 14.6668 13C14.6668 13.2761 14.443 13.5 14.1668 13.5H0.833496C0.557354 13.5 0.333496 13.2761 0.333496 13C0.333496 12.7239 0.557354 12.5 0.833496 12.5H1.00016L1.00016 3.11304C1.00016 2.75694 1.00015 2.45971 1.01999 2.21689C1.04068 1.96363 1.08542 1.72586 1.19998 1.50102C1.37575 1.15605 1.65622 0.87559 2.00118 0.699822C2.22602 0.585259 2.46379 0.540519 2.71705 0.519827C2.95987 0.499987 3.2571 0.499993 3.61321 0.5ZM2.00016 12.5H7.66683V3.13333C7.66683 2.75172 7.66644 2.49557 7.65032 2.29832C7.63468 2.1069 7.60676 2.01538 7.576 1.95501C7.49611 1.79821 7.36862 1.67072 7.21182 1.59083C7.15145 1.56007 7.05993 1.53214 6.86851 1.51651C6.67126 1.50039 6.41511 1.5 6.0335 1.5H3.6335C3.25188 1.5 2.99573 1.50039 2.79848 1.51651C2.60707 1.53214 2.51554 1.56007 2.45517 1.59083C2.29837 1.67072 2.17089 1.79821 2.09099 1.95501C2.06023 2.01538 2.03231 2.1069 2.01667 2.29832C2.00055 2.49557 2.00016 2.75172 2.00016 3.13333V12.5ZM8.66683 6.83333V12.5H13.0002V8.46667C13.0002 8.08505 12.9998 7.82891 12.9837 7.63165C12.968 7.44024 12.9401 7.34872 12.9093 7.28834C12.8294 7.13154 12.702 7.00406 12.5452 6.92416C12.4848 6.8934 12.3933 6.86548 12.2018 6.84984C12.0046 6.83372 11.7484 6.83333 11.3668 6.83333H8.66683ZM3.3335 3.66667C3.3335 3.39052 3.55735 3.16667 3.8335 3.16667H5.8335C6.10964 3.16667 6.3335 3.39052 6.3335 3.66667C6.3335 3.94281 6.10964 4.16667 5.8335 4.16667H3.8335C3.55735 4.16667 3.3335 3.94281 3.3335 3.66667ZM3.3335 6.33333C3.3335 6.05719 3.55735 5.83333 3.8335 5.83333H5.8335C6.10964 5.83333 6.3335 6.05719 6.3335 6.33333C6.3335 6.60948 6.10964 6.83333 5.8335 6.83333H3.8335C3.55735 6.83333 3.3335 6.60948 3.3335 6.33333ZM3.3335 9C3.3335 8.72386 3.55735 8.5 3.8335 8.5H5.8335C6.10964 8.5 6.3335 8.72386 6.3335 9C6.3335 9.27614 6.10964 9.5 5.8335 9.5H3.8335C3.55735 9.5 3.3335 9.27614 3.3335 9Z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">Python Full Stack Developer - Django/Flask/FastAPI</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">Qcentrio</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>4-8 yrs</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Kolkata</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-index="8" class="slick-slide" tabindex="-1" aria-hidden="true" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="flex size-11 min-w-11 items-center justify-center self-center rounded bg-decorative-surface-03">
                                                        <svg viewBox="0 0 15 14" width="24" height="24" fill="#5A0A40">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.61321 0.5H6.05379C6.40989 0.499993 6.70712 0.499987 6.94994 0.519827C7.2032 0.540519 7.44097 0.585259 7.66581 0.699822C8.01078 0.87559 8.29124 1.15605 8.46701 1.50102C8.58157 1.72586 8.62631 1.96363 8.647 2.21689C8.66684 2.45971 8.66684 2.75695 8.66683 3.11306V5.83333H11.3871C11.7432 5.83333 12.0405 5.83332 12.2833 5.85316C12.5365 5.87385 12.7743 5.91859 12.9991 6.03316C13.3441 6.20892 13.6246 6.48939 13.8003 6.83435C13.9149 7.05919 13.9596 7.29696 13.9803 7.55022C14.0002 7.79304 14.0002 8.09027 14.0002 8.44637V12.5H14.1668C14.443 12.5 14.6668 12.7239 14.6668 13C14.6668 13.2761 14.443 13.5 14.1668 13.5H0.833496C0.557354 13.5 0.333496 13.2761 0.333496 13C0.333496 12.7239 0.557354 12.5 0.833496 12.5H1.00016L1.00016 3.11304C1.00016 2.75694 1.00015 2.45971 1.01999 2.21689C1.04068 1.96363 1.08542 1.72586 1.19998 1.50102C1.37575 1.15605 1.65622 0.87559 2.00118 0.699822C2.22602 0.585259 2.46379 0.540519 2.71705 0.519827C2.95987 0.499987 3.2571 0.499993 3.61321 0.5ZM2.00016 12.5H7.66683V3.13333C7.66683 2.75172 7.66644 2.49557 7.65032 2.29832C7.63468 2.1069 7.60676 2.01538 7.576 1.95501C7.49611 1.79821 7.36862 1.67072 7.21182 1.59083C7.15145 1.56007 7.05993 1.53214 6.86851 1.51651C6.67126 1.50039 6.41511 1.5 6.0335 1.5H3.6335C3.25188 1.5 2.99573 1.50039 2.79848 1.51651C2.60707 1.53214 2.51554 1.56007 2.45517 1.59083C2.29837 1.67072 2.17089 1.79821 2.09099 1.95501C2.06023 2.01538 2.03231 2.1069 2.01667 2.29832C2.00055 2.49557 2.00016 2.75172 2.00016 3.13333V12.5ZM8.66683 6.83333V12.5H13.0002V8.46667C13.0002 8.08505 12.9998 7.82891 12.9837 7.63165C12.968 7.44024 12.9401 7.34872 12.9093 7.28834C12.8294 7.13154 12.702 7.00406 12.5452 6.92416C12.4848 6.8934 12.3933 6.86548 12.2018 6.84984C12.0046 6.83372 11.7484 6.83333 11.3668 6.83333H8.66683ZM3.3335 3.66667C3.3335 3.39052 3.55735 3.16667 3.8335 3.16667H5.8335C6.10964 3.16667 6.3335 3.39052 6.3335 3.66667C6.3335 3.94281 6.10964 4.16667 5.8335 4.16667H3.8335C3.55735 4.16667 3.3335 3.94281 3.3335 3.66667ZM3.3335 6.33333C3.3335 6.05719 3.55735 5.83333 3.8335 5.83333H5.8335C6.10964 5.83333 6.3335 6.05719 6.3335 6.33333C6.3335 6.60948 6.10964 6.83333 5.8335 6.83333H3.8335C3.55735 6.83333 3.3335 6.60948 3.3335 6.33333ZM3.3335 9C3.3335 8.72386 3.55735 8.5 3.8335 8.5H5.8335C6.10964 8.5 6.3335 8.72386 6.3335 9C6.3335 9.27614 6.10964 9.5 5.8335 9.5H3.8335C3.55735 9.5 3.3335 9.27614 3.3335 9Z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">Python Developer</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">SRS Business Solutions India Private Limited</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>5-10 yrs</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Bengaluru</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-index="9" class="slick-slide" tabindex="-1" aria-hidden="true" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="flex size-11 min-w-11 items-center justify-center self-center rounded bg-decorative-surface-03">
                                                        <svg viewBox="0 0 15 14" width="24" height="24" fill="#5A0A40">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.61321 0.5H6.05379C6.40989 0.499993 6.70712 0.499987 6.94994 0.519827C7.2032 0.540519 7.44097 0.585259 7.66581 0.699822C8.01078 0.87559 8.29124 1.15605 8.46701 1.50102C8.58157 1.72586 8.62631 1.96363 8.647 2.21689C8.66684 2.45971 8.66684 2.75695 8.66683 3.11306V5.83333H11.3871C11.7432 5.83333 12.0405 5.83332 12.2833 5.85316C12.5365 5.87385 12.7743 5.91859 12.9991 6.03316C13.3441 6.20892 13.6246 6.48939 13.8003 6.83435C13.9149 7.05919 13.9596 7.29696 13.9803 7.55022C14.0002 7.79304 14.0002 8.09027 14.0002 8.44637V12.5H14.1668C14.443 12.5 14.6668 12.7239 14.6668 13C14.6668 13.2761 14.443 13.5 14.1668 13.5H0.833496C0.557354 13.5 0.333496 13.2761 0.333496 13C0.333496 12.7239 0.557354 12.5 0.833496 12.5H1.00016L1.00016 3.11304C1.00016 2.75694 1.00015 2.45971 1.01999 2.21689C1.04068 1.96363 1.08542 1.72586 1.19998 1.50102C1.37575 1.15605 1.65622 0.87559 2.00118 0.699822C2.22602 0.585259 2.46379 0.540519 2.71705 0.519827C2.95987 0.499987 3.2571 0.499993 3.61321 0.5ZM2.00016 12.5H7.66683V3.13333C7.66683 2.75172 7.66644 2.49557 7.65032 2.29832C7.63468 2.1069 7.60676 2.01538 7.576 1.95501C7.49611 1.79821 7.36862 1.67072 7.21182 1.59083C7.15145 1.56007 7.05993 1.53214 6.86851 1.51651C6.67126 1.50039 6.41511 1.5 6.0335 1.5H3.6335C3.25188 1.5 2.99573 1.50039 2.79848 1.51651C2.60707 1.53214 2.51554 1.56007 2.45517 1.59083C2.29837 1.67072 2.17089 1.79821 2.09099 1.95501C2.06023 2.01538 2.03231 2.1069 2.01667 2.29832C2.00055 2.49557 2.00016 2.75172 2.00016 3.13333V12.5ZM8.66683 6.83333V12.5H13.0002V8.46667C13.0002 8.08505 12.9998 7.82891 12.9837 7.63165C12.968 7.44024 12.9401 7.34872 12.9093 7.28834C12.8294 7.13154 12.702 7.00406 12.5452 6.92416C12.4848 6.8934 12.3933 6.86548 12.2018 6.84984C12.0046 6.83372 11.7484 6.83333 11.3668 6.83333H8.66683ZM3.3335 3.66667C3.3335 3.39052 3.55735 3.16667 3.8335 3.16667H5.8335C6.10964 3.16667 6.3335 3.39052 6.3335 3.66667C6.3335 3.94281 6.10964 4.16667 5.8335 4.16667H3.8335C3.55735 4.16667 3.3335 3.94281 3.3335 3.66667ZM3.3335 6.33333C3.3335 6.05719 3.55735 5.83333 3.8335 5.83333H5.8335C6.10964 5.83333 6.3335 6.05719 6.3335 6.33333C6.3335 6.60948 6.10964 6.83333 5.8335 6.83333H3.8335C3.55735 6.83333 3.3335 6.60948 3.3335 6.33333ZM3.3335 9C3.3335 8.72386 3.55735 8.5 3.8335 8.5H5.8335C6.10964 8.5 6.3335 8.72386 6.3335 9C6.3335 9.27614 6.10964 9.5 5.8335 9.5H3.8335C3.55735 9.5 3.3335 9.27614 3.3335 9Z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">Python Developer - CAD Applications</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">Maimsd Technology</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>5-10 yrs</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Bengaluru</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-index="10" class="slick-slide" tabindex="-1" aria-hidden="true" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="border-border-subtle flex size-11 min-w-11 items-center self-start rounded border border-solid p-[1px]">
                                                        <img src="https://media.monsterindia.com/logos/xnipunainx/jdlogo.gif" alt="Tech Mahindra Limited" class="w-11"/>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">Python API developer - Bengaluru/Hyderabad</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">Tech Mahindra Limited</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>4-9 yrs</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Hyderabad, Bengaluru</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-index="11" class="slick-slide" tabindex="-1" aria-hidden="true" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="flex size-11 min-w-11 items-center justify-center self-center rounded bg-decorative-surface-03">
                                                        <svg viewBox="0 0 15 14" width="24" height="24" fill="#5A0A40">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.61321 0.5H6.05379C6.40989 0.499993 6.70712 0.499987 6.94994 0.519827C7.2032 0.540519 7.44097 0.585259 7.66581 0.699822C8.01078 0.87559 8.29124 1.15605 8.46701 1.50102C8.58157 1.72586 8.62631 1.96363 8.647 2.21689C8.66684 2.45971 8.66684 2.75695 8.66683 3.11306V5.83333H11.3871C11.7432 5.83333 12.0405 5.83332 12.2833 5.85316C12.5365 5.87385 12.7743 5.91859 12.9991 6.03316C13.3441 6.20892 13.6246 6.48939 13.8003 6.83435C13.9149 7.05919 13.9596 7.29696 13.9803 7.55022C14.0002 7.79304 14.0002 8.09027 14.0002 8.44637V12.5H14.1668C14.443 12.5 14.6668 12.7239 14.6668 13C14.6668 13.2761 14.443 13.5 14.1668 13.5H0.833496C0.557354 13.5 0.333496 13.2761 0.333496 13C0.333496 12.7239 0.557354 12.5 0.833496 12.5H1.00016L1.00016 3.11304C1.00016 2.75694 1.00015 2.45971 1.01999 2.21689C1.04068 1.96363 1.08542 1.72586 1.19998 1.50102C1.37575 1.15605 1.65622 0.87559 2.00118 0.699822C2.22602 0.585259 2.46379 0.540519 2.71705 0.519827C2.95987 0.499987 3.2571 0.499993 3.61321 0.5ZM2.00016 12.5H7.66683V3.13333C7.66683 2.75172 7.66644 2.49557 7.65032 2.29832C7.63468 2.1069 7.60676 2.01538 7.576 1.95501C7.49611 1.79821 7.36862 1.67072 7.21182 1.59083C7.15145 1.56007 7.05993 1.53214 6.86851 1.51651C6.67126 1.50039 6.41511 1.5 6.0335 1.5H3.6335C3.25188 1.5 2.99573 1.50039 2.79848 1.51651C2.60707 1.53214 2.51554 1.56007 2.45517 1.59083C2.29837 1.67072 2.17089 1.79821 2.09099 1.95501C2.06023 2.01538 2.03231 2.1069 2.01667 2.29832C2.00055 2.49557 2.00016 2.75172 2.00016 3.13333V12.5ZM8.66683 6.83333V12.5H13.0002V8.46667C13.0002 8.08505 12.9998 7.82891 12.9837 7.63165C12.968 7.44024 12.9401 7.34872 12.9093 7.28834C12.8294 7.13154 12.702 7.00406 12.5452 6.92416C12.4848 6.8934 12.3933 6.86548 12.2018 6.84984C12.0046 6.83372 11.7484 6.83333 11.3668 6.83333H8.66683ZM3.3335 3.66667C3.3335 3.39052 3.55735 3.16667 3.8335 3.16667H5.8335C6.10964 3.16667 6.3335 3.39052 6.3335 3.66667C6.3335 3.94281 6.10964 4.16667 5.8335 4.16667H3.8335C3.55735 4.16667 3.3335 3.94281 3.3335 3.66667ZM3.3335 6.33333C3.3335 6.05719 3.55735 5.83333 3.8335 5.83333H5.8335C6.10964 5.83333 6.3335 6.05719 6.3335 6.33333C6.3335 6.60948 6.10964 6.83333 5.8335 6.83333H3.8335C3.55735 6.83333 3.3335 6.60948 3.3335 6.33333ZM3.3335 9C3.3335 8.72386 3.55735 8.5 3.8335 8.5H5.8335C6.10964 8.5 6.3335 8.72386 6.3335 9C6.3335 9.27614 6.10964 9.5 5.8335 9.5H3.8335C3.55735 9.5 3.3335 9.27614 3.3335 9Z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">SQL Developer (Python/ADF)</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">Leuwint Technologies</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>5-9 yrs</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Gurugram</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-index="12" class="slick-slide" tabindex="-1" aria-hidden="true" style="outline:none;width:7.6923076923076925%">
                                    <div>
                                        <div class="flex w-auto cursor-pointer flex-col rounded-lg border-[1px] border-darkKnight-100 mr-3 h-[208px] !w-[95%] bg-surface-primary-normal lg:!w-[386px]">
                                            <div class="flex min-h-max justify-between gap-2 p-3 ">
                                                <div class="flex items-center gap-2">
                                                    <div class="flex size-11 min-w-11 items-center justify-center self-center rounded bg-decorative-surface-03">
                                                        <svg viewBox="0 0 15 14" width="24" height="24" fill="#5A0A40">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.61321 0.5H6.05379C6.40989 0.499993 6.70712 0.499987 6.94994 0.519827C7.2032 0.540519 7.44097 0.585259 7.66581 0.699822C8.01078 0.87559 8.29124 1.15605 8.46701 1.50102C8.58157 1.72586 8.62631 1.96363 8.647 2.21689C8.66684 2.45971 8.66684 2.75695 8.66683 3.11306V5.83333H11.3871C11.7432 5.83333 12.0405 5.83332 12.2833 5.85316C12.5365 5.87385 12.7743 5.91859 12.9991 6.03316C13.3441 6.20892 13.6246 6.48939 13.8003 6.83435C13.9149 7.05919 13.9596 7.29696 13.9803 7.55022C14.0002 7.79304 14.0002 8.09027 14.0002 8.44637V12.5H14.1668C14.443 12.5 14.6668 12.7239 14.6668 13C14.6668 13.2761 14.443 13.5 14.1668 13.5H0.833496C0.557354 13.5 0.333496 13.2761 0.333496 13C0.333496 12.7239 0.557354 12.5 0.833496 12.5H1.00016L1.00016 3.11304C1.00016 2.75694 1.00015 2.45971 1.01999 2.21689C1.04068 1.96363 1.08542 1.72586 1.19998 1.50102C1.37575 1.15605 1.65622 0.87559 2.00118 0.699822C2.22602 0.585259 2.46379 0.540519 2.71705 0.519827C2.95987 0.499987 3.2571 0.499993 3.61321 0.5ZM2.00016 12.5H7.66683V3.13333C7.66683 2.75172 7.66644 2.49557 7.65032 2.29832C7.63468 2.1069 7.60676 2.01538 7.576 1.95501C7.49611 1.79821 7.36862 1.67072 7.21182 1.59083C7.15145 1.56007 7.05993 1.53214 6.86851 1.51651C6.67126 1.50039 6.41511 1.5 6.0335 1.5H3.6335C3.25188 1.5 2.99573 1.50039 2.79848 1.51651C2.60707 1.53214 2.51554 1.56007 2.45517 1.59083C2.29837 1.67072 2.17089 1.79821 2.09099 1.95501C2.06023 2.01538 2.03231 2.1069 2.01667 2.29832C2.00055 2.49557 2.00016 2.75172 2.00016 3.13333V12.5ZM8.66683 6.83333V12.5H13.0002V8.46667C13.0002 8.08505 12.9998 7.82891 12.9837 7.63165C12.968 7.44024 12.9401 7.34872 12.9093 7.28834C12.8294 7.13154 12.702 7.00406 12.5452 6.92416C12.4848 6.8934 12.3933 6.86548 12.2018 6.84984C12.0046 6.83372 11.7484 6.83333 11.3668 6.83333H8.66683ZM3.3335 3.66667C3.3335 3.39052 3.55735 3.16667 3.8335 3.16667H5.8335C6.10964 3.16667 6.3335 3.39052 6.3335 3.66667C6.3335 3.94281 6.10964 4.16667 5.8335 4.16667H3.8335C3.55735 4.16667 3.3335 3.94281 3.3335 3.66667ZM3.3335 6.33333C3.3335 6.05719 3.55735 5.83333 3.8335 5.83333H5.8335C6.10964 5.83333 6.3335 6.05719 6.3335 6.33333C6.3335 6.60948 6.10964 6.83333 5.8335 6.83333H3.8335C3.55735 6.83333 3.3335 6.60948 3.3335 6.33333ZM3.3335 9C3.3335 8.72386 3.55735 8.5 3.8335 8.5H5.8335C6.10964 8.5 6.3335 8.72386 6.3335 9C6.3335 9.27614 6.10964 9.5 5.8335 9.5H3.8335C3.55735 9.5 3.3335 9.27614 3.3335 9Z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex flex-col gap-[2px] break-words ">
                                                        <h3 class="line-clamp-1 text-ellipsis text-base font-bold text-darkKnight-700 hover:underline">Full Stack Developer (Python)</h3>
                                                        <span class="line-clamp-2 text-sm font-normal">
                                                            <a href="" target="_blank" style="color:#6e00be !important">Ztek Consulting</a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-2">
                                                    <span class="size-4 cursor-pointer self-end">
                                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="">
                                                            <path fill-rule="evenodd" d="M9.77 2.25h4.46c.81 0 1.47 0 2 .04.55.05 1.03.14 *********.36 1.28.93 1.64 *********4.32.92.37 **********.04 1.19.04 2V21a.75.75 0 0 1-1.12.65L12 17.86l-6.63 3.8A.75.75 0 0 1 4.25 21V7.77c0-.81 0-1.47.04-2 .05-.55.14-1.03.37-1.47.36-.7.93-1.28 1.64-1.64.44-.23.92-.32 1.47-.37.53-.04 1.19-.04 2-.04ZM7.89 3.79c.46-.04 1.06-.04 1.91-.04h4.4c.85 0 1.45 0 *********.04.72.1.92.2.42.22.77.57.98.99.1.2.17.46.21.91.04.46.04 1.06.04 1.91v11.9l-5.88-3.35a.75.75 0 0 0-.74 0L5.75 19.7V7.8c0-.85 0-1.45.04-1.9.04-.46.1-.72.2-.92.22-.42.57-.77.99-.98.2-.1.46-.17.91-.21Z" fill="#59566C"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="relative flex grow flex-col gap-2 border-darkKnight-100 pl-3 pr-3">
                                                <div class="flex items-center gap-2 text-xs text-darkKnight-700">
                                                    <div class="flex h-4 items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path d="M11.88 2.25a220.06 220.06 0 0 0 .24 0c.81 0 1.37 0 1.85.13a3.75 3.75 0 0 1 2.78 3.87h2.08c.53 0 .98 0 1.34.03a2.68 2.68 0 0 1 2.55 2.55c.03.36.03.8.03 1.34v7.66c0 .53 0 .98-.03 1.34a2.68 2.68 0 0 1-2.55 2.55c-.36.03-.8.03-1.34.03H5.17c-.53 0-.98 0-1.34-.03a2.8 2.8 0 0 1-1.08-.27 2.75 2.75 0 0 1-1.2-1.2 2.8 2.8 0 0 1-.27-1.08c-.03-.36-.03-.8-.03-1.34v-7.66c0-.53 0-.98.03-1.34a2.68 2.68 0 0 1 2.55-2.55c.36-.03.8-.03 1.34-.03h2.08a3.75 3.75 0 0 1 2.78-3.87 7.5 7.5 0 0 1 1.85-.13Zm-4.63 5.5H5.2c-.57 0-.96 0-1.25.02-.29.03-.43.07-.52.12-.23.12-.42.3-.54.54-.05.1-.1.23-.12.52-.02.3-.02.68-.02 1.25v7.6c0 .57 0 .96.02 1.25.03.29.07.43.12.52.12.23.3.42.54.54.1.05.23.1.52.12.3.02.68.02 1.25.02h2.05V7.75Zm1.5 12.5h6.5V7.75h-6.5v12.5Zm8 0h2.05c.57 0 .96 0 1.25-.02.29-.03.43-.07.52-.12.23-.12.42-.3.54-.54.05-.1.1-.23.12-.52.02-.3.02-.68.02-1.25v-7.6c0-.57 0-.96-.02-1.25a1.36 1.36 0 0 0-.12-.52 1.25 1.25 0 0 0-.54-.54c-.1-.05-.23-.1-.52-.12-.3-.02-.68-.02-1.25-.02h-2.05v12.5Zm-1.5-14a3.47 3.47 0 0 0-.08-.83 2.25 2.25 0 0 0-1.59-1.6A7.1 7.1 0 0 0 12 3.76c-.98 0-1.32 0-1.58.08a2.25 2.25 0 0 0-1.67 2.42h6.5Z"></path>
                                                        </svg>
                                                        <label>4-10 yrs</label>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-3 text-xs text-darkKnight-700">
                                                    <div class="flex items-center gap-1">
                                                        <svg viewBox="0 0 24 24" fill="#59566C" width="16" height="16">
                                                            <path fill-rule="evenodd" d="M12 2.75C8 2.75 4.75 6 4.75 10c0 2.4 1.32 4.22 3.04 6.01.41.43.84.85 1.28 1.28l.05.05c.44.44.9.89 1.33 1.34A16 16 0 0 1 12 20.54a16 16 0 0 1 1.55-1.86c.43-.45.89-.9 1.33-1.34l.05-.05c.44-.43.87-.85 1.28-1.28 1.72-1.8 3.04-3.6 3.04-6.01 0-4-3.25-7.25-7.25-7.25ZM3.25 10a8.75 8.75 0 0 1 17.5 0c0 3.02-1.68 5.2-3.46 7.05-.43.45-.87.88-1.3 1.3l-.05.06c-.45.44-.9.87-1.3 1.3-.83.88-1.52 1.72-1.97 2.63a.75.75 0 0 1-1.34 0c-.45-.9-1.14-1.75-1.97-2.62-.4-.44-.85-.87-1.3-1.31l-.05-.06c-.43-.42-.87-.85-1.3-1.3C4.93 15.2 3.25 13.02 3.25 10ZM12 7.25a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM8.25 9.5a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0Z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <label class="line-clamp-1 h-4 w-[80%] text-ellipsis">Delhi, Kolkata, Mumbai</label>
                                                    </div>
                                                    <div class="flex items-center gap-3"></div>
                                                </div>
                                            </div>
                                            <div class="flex h-12 items-center justify-between p-3 pl-3">
                                                <div class="flex gap-2"></div>
                                                <div class="flex items-center gap-4">
                                                    <div class="">
                                                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-3 py-2 text-sm font-normal text-trusted-600 border bg-primary-400 border-primary-400 hover:bg-trusted-200 hover:border-trusted-200 w-full md:!w-fit md:!h-[36px] !h-[36px]" id="applyBtn">
                                                            <svg viewBox="0 0 16 16" fill="#6e00be" width="16" height="16">
                                                                <path d="M10.1953 1L7.48465 6.63289L11 7.94592L2.97407 14.8707L5.59997 9.23781L2 7.88247L10.1953 1Z"></path>
                                                            </svg>
                                                            Quick Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="inline-flex items-center justify-center gap-1 rounded-3xl px-4 py-3 text-sm font-semibold text-trusted-600 bg-white border-transparent hover:bg-primary-400 hover:border-primary-400 slick-arrow slick-next !bg-surface-primary-normal md:right-12 lg:right-11 xl:right-0 z-[9] size-8 rounded-full border border-solid !border-border-moderate !p-0 md:inline-flex lg:inline-flex" style="box-shadow:0px 0px 10px 0px #00000040">
                            <svg fill="#6E00BE" viewBox="0 0 24 24" height="21" width="21" class="absolute -rotate-90">
                                <path fill-rule="evenodd" d="M5.47 9.47c.3-.3.77-.3 1.06 0L12 14.94l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6 6c-.3.3-.77.3-1.06 0l-6-6a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <script>
            $RC = function(b, c, e) {
                c = document.getElementById(c);
                c.parentNode.removeChild(c);
                var a = document.getElementById(b);
                if (a) {
                    b = a.previousSibling;
                    if (e)
                        b.data = "$!",
                        a.setAttribute("data-dgst", e);
                    else {
                        e = b.parentNode;
                        a = b.nextSibling;
                        var f = 0;
                        do {
                            if (a && 8 === a.nodeType) {
                                var d = a.data;
                                if ("/$" === d)
                                    if (0 === f)
                                        break;
                                    else
                                        f--;
                                else
                                    "$" !== d && "$?" !== d && "$!" !== d || f++
                            }
                            d = a.nextSibling;
                            e.removeChild(a);
                            a = d
                        } while (a);
                        for (; c.firstChild; )
                            e.insertBefore(c.firstChild, a);
                        b.data = "$"
                    }
                    b._reactRetry && b._reactRetry()
                }
            }
            ;
            $RC("B:0", "S:0")
        </script>
    </body>
</html>
