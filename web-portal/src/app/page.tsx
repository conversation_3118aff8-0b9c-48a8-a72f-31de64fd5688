"use client";
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import ReactMarkdown from "react-markdown";

// Define types for better TypeScript support
interface JobData {
  [key: string]: string | number | boolean | string[] | null | undefined;
}

interface FormData {
  job_title: string;
  location: string;
  num_jobs: number;
  search_term: string;
  results_wanted: number;
}

const API_BASE =
  process.env.NEXT_PUBLIC_API_BASE_URL ||
  "https://cadd044e8d98.ngrok-free.app";

// Debug logging
console.log("🔧 Environment variable NEXT_PUBLIC_API_BASE_URL:", process.env.NEXT_PUBLIC_API_BASE_URL);
console.log("🔧 Final API_BASE:", API_BASE);

const portals = [
  {
    key: "foundit",
    label: "Foundit.in",
    endpoint: `${API_BASE}/foundit/scrape`,
    params: ["job_title", "location", "num_jobs"],
    method: "POST",
  },
  {
    key: "glassdoor",
    label: "Glassdoor",
    endpoint: `${API_BASE}/glassdoor/scrape_jobs_parallel`,
    params: ["job_title", "location", "num_jobs"],
    method: "POST",
  },
  {
    key: "simplyhired",
    label: "SimplyHired",
    endpoint: `${API_BASE}/simplyhired/scrape_simplyhired`,
    params: ["job_title", "location", "num_jobs"],
    method: "POST",
  },
  {
    key: "ziprecruiter",
    label: "ZipRecruiter",
    endpoint: `${API_BASE}/ziprecruiter/scrape_ziprecruiter`,
    params: ["job_title", "location", "num_jobs"],
    method: "POST",
  },
  {
    key: "linkedin",
    label: "LinkedIn",
    endpoint: `${API_BASE}/linkedin/scrape-linkedin/`,
    params: ["search_term", "location", "results_wanted"],
    method: "POST",
  },
  {
    key: "indeed",
    label: "Indeed",
    endpoint: `${API_BASE}/scrape-indeed/`,
    params: ["search_term", "location", "results_wanted"],
    method: "POST",
  },
  {
    key: "naukri",
    label: "Naukri",
    endpoint: `${API_BASE}/naukri/scrape`,
    params: ["job_title", "location", "num_jobs"],
    method: "POST",
  },
];

function GradientBG({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-black via-purple-900 via-purple-800 to-purple-600 flex flex-col items-center justify-start sm:justify-center relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-transparent to-purple-500/10 animate-pulse"></div>
      <div className="absolute top-0 left-0 w-32 h-32 sm:w-72 sm:h-72 bg-purple-500/20 rounded-full blur-3xl animate-bounce"></div>
      <div className="absolute bottom-0 right-0 w-48 h-48 sm:w-96 sm:h-96 bg-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="relative z-10 w-full perspective-3d flex flex-col min-h-screen">{children}</div>
    </div>
  );
}

export default function Home() {
  const [activeTab, setActiveTab] = useState("foundit");

  // Auto-scroll active tab into view on mobile
  const handleTabChange = (tabKey: string) => {
    setActiveTab(tabKey);

    // Scroll the active tab into view on mobile
    setTimeout(() => {
      const activeButton = document.querySelector(`[data-tab="${tabKey}"]`);
      if (activeButton) {
        activeButton.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        });
      }
    }, 100);
  };
  const [form, setForm] = useState<FormData>({
    job_title: "Data Scientist",
    location: "Pune",
    num_jobs: 3,
    search_term: "Data Scientist",
    results_wanted: 3,
  });
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<JobData[] | null>(null);
  const [error, setError] = useState<string | null>(null);

  const portal = portals.find((p) => p.key === activeTab)!;

  const handleInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm((f) => ({ ...f, [e.target.name]: e.target.value }));
  };

  const handleSearch = async () => {
    setLoading(true);
    setError(null);
    setResults(null);

    try {
      console.log("🔍 Starting search for portal:", portal.key);
      console.log("🌐 API Base URL:", API_BASE);
      console.log("📡 Endpoint:", portal.endpoint);
      console.log("📝 Form data:", form);

      switch (portal.method) {
        case "GET": {
          const params = new URLSearchParams();
          portal.params.forEach((param) => {
            const value = form[param as keyof typeof form];
            if (value !== undefined && value !== "") {
              params.append(param, String(value));
            }
          });
          const url = `${
            portal.endpoint
          }?${params.toString()}&_t=${Date.now()}`;
          console.log("🔗 GET URL:", url);

          const res = await fetch(url, {
            cache: "no-cache",
            headers: {
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
            mode: "cors",
          });
          console.log("📊 Response status:", res.status);
          console.log(
            "📊 Response headers:",
            Object.fromEntries(res.headers.entries())
          );

          if (!res.ok) {
            const errorText = await res.text();
            console.error("❌ Error response:", errorText);
            throw new Error(errorText);
          }

          const responseText = await res.text();
          console.log(
            "📄 Raw response:",
            responseText.substring(0, 500) + "..."
          );

          let data;
          try {
            data = JSON.parse(responseText);
          } catch (parseError) {
            console.error("❌ JSON parse error:", parseError);
            console.error("📄 Full response:", responseText);
            throw new Error("Invalid JSON response from server");
          }

          console.log("✅ Success response:", data);
          setResults(data.jobs || data.scraped_jobs || data);
          break;
        }
        case "POST": {
          // Different request body format for different scrapers
          let requestBody;
          if (portal.key === "linkedin") {
            requestBody = {
              search_term: form.search_term || form.job_title,
              location: form.location,
              results_wanted: form.results_wanted || form.num_jobs,
              linkedin_fetch_description: true,
            };
          } else if (portal.key === "indeed") {
            requestBody = {
              search_term: form.search_term || form.job_title,
              location: form.location,
              results_wanted: form.results_wanted || form.num_jobs,
            };
          } else if (portal.key === "naukri") {
            // For Naukri with enhanced detailed information
            requestBody = {
              job_title: form.job_title,
              location: form.location,
              num_jobs: form.num_jobs,
              detailed: true,  // Enable detailed mode for comprehensive job data
            };
          } else {
            // For other custom scrapers (foundit, glassdoor, simplyhired, ziprecruiter)
            requestBody = {
              job_title: form.job_title,
              location: form.location,
              num_jobs: form.num_jobs,
            };
          }
          console.log("📤 POST request body:", requestBody);

          const res = await fetch(`${portal.endpoint}?_t=${Date.now()}`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
            cache: "no-cache",
            mode: "cors",
            body: JSON.stringify(requestBody),
          });
          console.log("📊 Response status:", res.status);
          console.log(
            "📊 Response headers:",
            Object.fromEntries(res.headers.entries())
          );

          if (!res.ok) {
            const errorText = await res.text();
            console.error("❌ Error response:", errorText);
            throw new Error(errorText);
          }

          const responseText = await res.text();
          console.log(
            "📄 Raw response:",
            responseText.substring(0, 500) + "..."
          );

          let data;
          try {
            data = JSON.parse(responseText);
          } catch (parseError) {
            console.error("❌ JSON parse error:", parseError);
            console.error("📄 Full response:", responseText);
            throw new Error("Invalid JSON response from server");
          }

          console.log("✅ Success response:", data);
          setResults(data.jobs || data.scraped_jobs || data);
          break;
        }
        default:
          throw new Error(`Unsupported method: ${portal.method}`);
      }
    } catch (err) {
      console.error("💥 Search error:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  return (
    <GradientBG>
      <div className="w-full max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-6 lg:py-8">
        <div className="text-center mb-4 sm:mb-6 lg:mb-8 animate-fade-in-up">
          <h1 className="text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black gradient-text mb-2 sm:mb-4 drop-shadow-2xl animate-glow leading-tight">
            Job Scraper Portal
          </h1>
          <p className="text-sm sm:text-base md:text-lg lg:text-xl text-purple-200 text-center mb-2 sm:mb-4 font-medium px-2">
            Unified job search across 7 major platforms
          </p>
          <p className="text-xs sm:text-sm text-purple-300/70 text-center mb-3 sm:mb-6 font-mono break-all px-2">
            API: {API_BASE}
          </p>
          <div className="flex flex-wrap justify-center gap-1 sm:gap-2 text-xs text-purple-300/60 px-2">
            <span className="px-2 py-1 bg-purple-800/50 rounded-full hover-lift transition-all duration-200">
              Foundit.in
            </span>
            <span className="px-2 py-1 bg-purple-800/50 rounded-full hover-lift transition-all duration-200">
              Glassdoor
            </span>
            <span className="px-2 py-1 bg-purple-800/50 rounded-full hover-lift transition-all duration-200">
              SimplyHired
            </span>
            <span className="px-2 py-1 bg-purple-800/50 rounded-full hover-lift transition-all duration-200">
              ZipRecruiter
            </span>
            <span className="px-2 py-1 bg-purple-800/50 rounded-full hover-lift transition-all duration-200">
              LinkedIn
            </span>
            <span className="px-2 py-1 bg-purple-800/50 rounded-full hover-lift transition-all duration-200">
              Indeed
            </span>
            <span className="px-2 py-1 bg-purple-800/50 rounded-full hover-lift transition-all duration-200">
              Naukri
            </span>
          </div>
        </div>
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          {/* Mobile: Horizontal scrollable tabs */}
          <div className="lg:hidden mb-4 sm:mb-6">
            <div className="scrollable-tabs-container bg-black/40 backdrop-blur-xl rounded-xl p-1.5 border border-purple-500/20 shadow-2xl shadow-purple-500/10">
              <div className="flex gap-1.5 overflow-x-auto scrollbar-hide pb-1 px-2" style={{ scrollPaddingLeft: '8px', scrollPaddingRight: '8px' }}>
                {portals.map((p) => (
                  <button
                    key={p.key}
                    data-tab={p.key}
                    onClick={() => handleTabChange(p.key)}
                    className={`text-xs sm:text-sm font-semibold px-3 sm:px-4 py-2 sm:py-2.5 rounded-lg whitespace-nowrap flex-shrink-0 min-w-fit transition-all duration-300 ${
                      activeTab === p.key
                        ? 'bg-gradient-to-r from-purple-600 to-purple-400 text-white shadow-lg shadow-purple-500/25 scale-105'
                        : 'text-purple-200 hover:bg-purple-800/30 hover:shadow-purple-500/20 hover:scale-102'
                    }`}
                  >
                    {p.label}
                  </button>
                ))}
              </div>
              {/* Scroll hint and tab indicator for mobile users */}
              <div className="text-center mt-1 sm:hidden">
                <div className="text-xs text-purple-300/60 flex items-center justify-center gap-2">
                  <span>👈</span>
                  <span>Swipe to see all {portals.length} portals</span>
                  <span>👉</span>
                </div>
                <div className="flex justify-center gap-1 mt-1">
                  {portals.map((p) => (
                    <div
                      key={p.key}
                      className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
                        activeTab === p.key
                          ? 'bg-purple-400 scale-125'
                          : 'bg-purple-600/40'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Desktop: Grid layout tabs */}
          <TabsList className="hidden lg:grid w-full grid-cols-7 xl:flex xl:justify-center xl:w-auto xl:mx-auto gap-2 bg-black/40 backdrop-blur-xl rounded-2xl p-2 mb-6 lg:mb-8 border border-purple-500/20 shadow-2xl shadow-purple-500/10">
            {portals.map((p) => (
              <TabsTrigger
                key={p.key}
                value={p.key}
                className="text-sm font-semibold px-4 xl:px-6 py-2 lg:py-3 rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-400 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-purple-500/25 transition-all duration-300 hover:bg-purple-800/30 text-purple-200 hover:shadow-purple-500/20 whitespace-nowrap flex-shrink-0"
              >
                <span className="truncate block">{p.label}</span>
              </TabsTrigger>
            ))}
          </TabsList>
          {portals.map((p) => (
            <TabsContent key={p.key} value={p.key} className="w-full">
              <Card className="mb-4 sm:mb-6 lg:mb-8 bg-black/40 backdrop-blur-xl border border-purple-500/20 shadow-2xl shadow-purple-500/10 hover:shadow-purple-500/20 transition-all duration-500 transform hover:scale-[1.01] sm:hover:scale-[1.02]">
                <CardContent className="p-3 sm:p-4 lg:p-6 flex flex-col gap-3 sm:gap-4 lg:gap-6">
                  <div className="flex flex-col gap-3 sm:gap-4">
                    {/* Job Title/Search Term - Full width on mobile */}
                    <div className="w-full">
                      <Label
                        htmlFor={
                          activeTab === "linkedin" ||
                          activeTab === "indeed"
                            ? "search_term"
                            : "job_title"
                        }
                        className="text-purple-200 font-semibold text-sm mb-2 block"
                      >
                        {activeTab === "linkedin" ||
                        activeTab === "indeed"
                          ? "Search Term"
                          : "Job Title"}
                      </Label>
                      <Input
                        id={
                          activeTab === "linkedin" ||
                          activeTab === "indeed"
                            ? "search_term"
                            : "job_title"
                        }
                        name={
                          activeTab === "linkedin" ||
                          activeTab === "indeed"
                            ? "search_term"
                            : "job_title"
                        }
                        value={
                          activeTab === "linkedin" ||
                          activeTab === "indeed"
                            ? form.search_term
                            : form.job_title
                        }
                        onChange={handleInput}
                        placeholder="e.g. Data Scientist"
                        className="w-full bg-black/50 border-purple-500/30 text-purple-100 placeholder-purple-400/50 focus:border-purple-400 focus:ring-purple-400/20 hover-lift transition-all duration-300 text-sm sm:text-base"
                      />
                    </div>

                    {/* Location and Number of Jobs - Side by side on mobile */}
                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                      <div className="flex-1">
                        <Label
                          htmlFor="location"
                          className="text-purple-200 font-semibold text-sm mb-2 block"
                        >
                          Location
                        </Label>
                        <Input
                          id="location"
                          name="location"
                          value={form.location}
                          onChange={handleInput}
                          placeholder="e.g. India"
                          className="w-full bg-black/50 border-purple-500/30 text-purple-100 placeholder-purple-400/50 focus:border-purple-400 focus:ring-purple-400/20 hover-lift transition-all duration-300 text-sm sm:text-base"
                        />
                      </div>
                      <div className="w-full sm:w-32">
                        <Label
                          htmlFor={
                            activeTab === "linkedin" ||
                            activeTab === "indeed"
                              ? "results_wanted"
                              : "num_jobs"
                          }
                          className="text-purple-200 font-semibold text-sm mb-2 block"
                        >
                          {activeTab === "linkedin" ||
                          activeTab === "indeed"
                            ? "Results"
                            : "# Jobs"}
                        </Label>
                        <Input
                          id={
                            activeTab === "linkedin" ||
                            activeTab === "indeed"
                              ? "results_wanted"
                              : "num_jobs"
                          }
                          name={
                            activeTab === "linkedin" ||
                            activeTab === "indeed"
                              ? "results_wanted"
                              : "num_jobs"
                          }
                          type="number"
                          min={1}
                          max={20}
                          value={
                            activeTab === "linkedin" ||
                            activeTab === "indeed"
                              ? form.results_wanted
                              : form.num_jobs
                          }
                          onChange={handleInput}
                          className="w-full bg-black/50 border-purple-500/30 text-purple-100 placeholder-purple-400/50 focus:border-purple-400 focus:ring-purple-400/20 hover-lift transition-all duration-300 text-sm sm:text-base"
                        />
                      </div>
                    </div>
                  </div>
                  <Button
                    onClick={handleSearch}
                    disabled={loading}
                    className="w-full mt-3 sm:mt-4 bg-gradient-to-r from-purple-600 to-purple-400 text-white font-bold py-3 sm:py-4 px-6 sm:px-8 rounded-lg sm:rounded-xl shadow-2xl shadow-purple-500/25 hover:from-purple-500 hover:to-purple-300 transition-all duration-300 transform hover:scale-[1.02] sm:hover:scale-105 hover:shadow-purple-500/40 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none relative overflow-hidden group text-sm sm:text-base"
                  >
                    {/* 3D Button glow effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-lg sm:rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                    <div className="relative z-10">
                      {loading ? (
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                          <span className="hidden xs:inline">Searching...</span>
                          <span className="xs:hidden">...</span>
                        </div>
                      ) : (
                        <span className="flex items-center justify-center gap-2">
                          <span>🔍</span>
                          <span>Search Jobs</span>
                        </span>
                      )}
                    </div>
                  </Button>
                  {error && (
                    <div className="text-red-400 font-semibold mt-3 sm:mt-4 p-3 sm:p-4 bg-red-900/20 border border-red-500/30 rounded-lg text-sm sm:text-base break-words">
                      <div className="flex items-start gap-2">
                        <span className="text-red-400 flex-shrink-0">⚠️</span>
                        <span className="flex-1">{error}</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                {results && Array.isArray(results) && results.length > 0 ? (
                  results.map((job: JobData, idx: number) => {
                    // Filter out fields with null or empty string values
                    const filteredJob = Object.fromEntries(
                      Object.entries(job).filter(
                        ([, value]) => value !== null && value !== ""
                      )
                    );
                    // Prepare fields for ordered display
                    const title = filteredJob.title;
                    const company_name = filteredJob.company_name;
                    const company_logo = filteredJob.company_logo;
                    const location = filteredJob.location;
                    const salary = filteredJob.salary || filteredJob.pay;
                    const jd_url = filteredJob.jd_url || filteredJob.job_url;
                    const job_description = filteredJob.job_description;
                    const experience = filteredJob.experience;
                    const skills = filteredJob.skills;
                    const posted_date = filteredJob.posted_date;
                    const applicants_count = filteredJob.applicants_count;
                    const job_id = filteredJob.job_id;
                    const company_description = filteredJob.company_description;
                    const role = filteredJob.role;
                    const industry = filteredJob.industry;
                    const function_area = filteredJob.function;
                    const job_type = filteredJob.job_type;
                    const extra_sections =
                      filteredJob.extra_sections &&
                      typeof filteredJob.extra_sections === "object"
                        ? filteredJob.extra_sections
                        : null;
                    // Collect all other fields except the above
                    const shownKeys = new Set([
                      "title",
                      "company_name",
                      "company_logo",
                      "location",
                      "salary",
                      "pay",
                      "jd_url",
                      "job_url",
                      "job_description",
                      "extra_sections",
                      "experience",
                      "skills",
                      "posted_date",
                      "applicants_count",
                      "job_id",
                      "company_description",
                      "role",
                      "industry",
                      "function",
                      "job_type",
                    ]);
                    const otherFields = Object.entries(filteredJob).filter(
                      ([key]) => !shownKeys.has(key)
                    );
                    return (
                      <Card
                        key={idx}
                        className="bg-black/40 backdrop-blur-xl border border-purple-500/20 shadow-2xl hover:scale-[1.01] sm:hover:scale-[1.02] hover:shadow-purple-500/25 transition-all duration-300 group relative overflow-hidden"
                        style={{ animationDelay: `${idx * 100}ms` }}
                      >
                        {/* 3D Card glow effect */}
                        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-lg blur-xl group-hover:blur-2xl transition-all duration-500"></div>
                        <div className="relative z-10">
                          <CardContent className="p-3 sm:p-4 lg:p-6 flex flex-col gap-2 sm:gap-3">
                            {/* Title */}
                            {title && (
                              <div className="text-lg sm:text-xl lg:text-2xl font-black text-purple-200 mb-2 sm:mb-3 bg-gradient-to-r from-purple-400/20 to-pink-400/20 p-2 sm:p-3 rounded-lg border border-purple-500/20 break-words">
                                {title}
                              </div>
                            )}
                            {/* Company name and logo */}
                            {(company_name || company_logo) && (
                              <div className="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 p-2 bg-purple-900/20 rounded-lg border border-purple-600/20">
                                {company_logo &&
                                typeof company_logo === "string" &&
                                (company_logo.startsWith("http://") ||
                                  company_logo.startsWith("https://")) ? (
                                  <Image
                                    src={company_logo}
                                    alt="Company logo"
                                    width={40}
                                    height={40}
                                    className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 rounded-full object-contain border-2 border-purple-500/30 shadow-lg flex-shrink-0"
                                  />
                                ) : null}
                                {company_name && (
                                  <span className="text-sm sm:text-base lg:text-lg font-bold text-purple-300 break-words flex-1">
                                    🏢 {company_name}
                                  </span>
                                )}
                              </div>
                            )}
                            {/* Company Description */}
                            {company_description && (
                              <div className="mb-2 sm:mb-3 p-2 sm:p-3 bg-purple-800/20 rounded-lg border border-purple-600/20">
                                <span className="font-bold text-purple-300 block mb-1 sm:mb-2 text-sm sm:text-base">
                                  🏢 About Company:
                                </span>
                                <div className="text-purple-100 text-xs sm:text-sm leading-relaxed prose prose-sm prose-invert max-w-none [&>*]:text-purple-100 [&>*]:text-xs [&>*]:sm:text-sm break-words">
                                  <ReactMarkdown>
                                    {String(company_description)}
                                  </ReactMarkdown>
                                </div>
                              </div>
                            )}
                            {/* Location */}
                            {location && (
                              <div className="mb-1.5 sm:mb-2 p-2 bg-purple-800/20 rounded-lg border border-purple-600/20">
                                <span className="font-bold text-purple-300 text-sm sm:text-base">
                                  📍 Location:
                                </span>{" "}
                                <span className="text-purple-100 font-medium text-sm sm:text-base break-words">
                                  {location}
                                </span>
                              </div>
                            )}
                            {/* Salary/Pay */}
                            {salary && (
                              <div className="mb-2 sm:mb-3 p-2 bg-green-900/20 rounded-lg border border-green-600/20">
                                <span className="font-bold text-green-300 text-sm sm:text-base">
                                  💰 Salary/Pay:
                                </span>{" "}
                                <span className="text-green-100 font-medium text-sm sm:text-base break-words">
                                  {salary}
                                </span>
                              </div>
                            )}
                            {/* Experience */}
                            {experience && (
                              <div className="mb-1.5 sm:mb-2 p-2 bg-blue-900/20 rounded-lg border border-blue-600/20">
                                <span className="font-bold text-blue-300 text-sm sm:text-base">
                                  🎯 Experience:
                                </span>{" "}
                                <span className="text-blue-100 font-medium text-sm sm:text-base">
                                  {experience}
                                </span>
                              </div>
                            )}
                            {/* Skills */}
                            {skills && Array.isArray(skills) && skills.length > 0 && (
                              <div className="mb-1.5 sm:mb-2 p-2 bg-orange-900/20 rounded-lg border border-orange-600/20">
                                <span className="font-bold text-orange-300 text-sm sm:text-base block mb-1">
                                  🛠️ Skills:
                                </span>
                                <div className="flex flex-wrap gap-1 sm:gap-1.5">
                                  {skills.map((skill, skillIdx) => (
                                    <span
                                      key={skillIdx}
                                      className="px-1.5 sm:px-2 py-0.5 sm:py-1 bg-orange-800/30 text-orange-100 rounded-full text-xs font-medium border border-orange-600/30 break-words"
                                    >
                                      {skill}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}
                            {/* Posted Date */}
                            {posted_date && (
                              <div className="mb-1.5 sm:mb-2 p-2 bg-indigo-900/20 rounded-lg border border-indigo-600/20">
                                <span className="font-bold text-indigo-300 text-sm sm:text-base">
                                  📅 Posted Date:
                                </span>{" "}
                                <span className="text-indigo-100 font-medium text-sm sm:text-base break-words">
                                  {posted_date}
                                </span>
                              </div>
                            )}
                            {/* Applicants Count */}
                            {applicants_count && (
                              <div className="mb-1.5 sm:mb-2 p-2 bg-pink-900/20 rounded-lg border border-pink-600/20">
                                <span className="font-bold text-pink-300 text-sm sm:text-base">
                                  👥 Applicants:
                                </span>{" "}
                                <span className="text-pink-100 font-medium text-sm sm:text-base">
                                  {applicants_count}
                                </span>
                              </div>
                            )}
                            {/* Job ID */}
                            {job_id && (
                              <div className="mb-1.5 sm:mb-2 p-2 bg-gray-900/20 rounded-lg border border-gray-600/20">
                                <span className="font-bold text-gray-300 text-sm sm:text-base">
                                  🆔 Job ID:
                                </span>{" "}
                                <span className="text-gray-100 font-medium font-mono text-xs sm:text-sm break-all">
                                  {job_id}
                                </span>
                              </div>
                            )}
                            {/* Role */}
                            {role && (
                              <div className="mb-1.5 sm:mb-2 p-2 bg-cyan-900/20 rounded-lg border border-cyan-600/20">
                                <span className="font-bold text-cyan-300 text-sm sm:text-base">
                                  👔 Role:
                                </span>{" "}
                                <span className="text-cyan-100 font-medium text-sm sm:text-base break-words">
                                  {role}
                                </span>
                              </div>
                            )}
                            {/* Industry */}
                            {industry && (
                              <div className="mb-1.5 sm:mb-2 p-2 bg-teal-900/20 rounded-lg border border-teal-600/20">
                                <span className="font-bold text-teal-300 text-sm sm:text-base">
                                  🏭 Industry:
                                </span>{" "}
                                <span className="text-teal-100 font-medium text-sm sm:text-base break-words">
                                  {industry}
                                </span>
                              </div>
                            )}
                            {/* Function Area */}
                            {function_area && (
                              <div className="mb-1.5 sm:mb-2 p-2 bg-emerald-900/20 rounded-lg border border-emerald-600/20">
                                <span className="font-bold text-emerald-300 text-sm sm:text-base">
                                  ⚙️ Function:
                                </span>{" "}
                                <span className="text-emerald-100 font-medium text-sm sm:text-base break-words">
                                  {function_area}
                                </span>
                              </div>
                            )}
                            {/* Job Type */}
                            {job_type && (
                              <div className="mb-1.5 sm:mb-2 p-2 bg-violet-900/20 rounded-lg border border-violet-600/20">
                                <span className="font-bold text-violet-300 text-sm sm:text-base">
                                  💼 Job Type:
                                </span>{" "}
                                <span className="text-violet-100 font-medium capitalize text-sm sm:text-base">
                                  {job_type}
                                </span>
                              </div>
                            )}
                            {/* Job URL */}
                            {jd_url && typeof jd_url === "string" && (
                              <div className="mb-1 sm:mb-2 p-2 bg-purple-900/20 rounded-lg border border-purple-600/20">
                                <span className="font-bold text-purple-300 text-sm sm:text-base block mb-1">
                                  🔗 Job URL:
                                </span>
                                <a
                                  href={jd_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-purple-400 hover:text-purple-300 hover:underline font-medium break-all transition-colors text-xs sm:text-sm block"
                                >
                                  {jd_url.length > 50 ? `${jd_url.substring(0, 50)}...` : jd_url}
                                </a>
                              </div>
                            )}
                            {/* Job Description (markdown) */}
                            {job_description &&
                              typeof job_description === "string" && (
                                <div className="mb-3 sm:mb-4 p-2 sm:p-3 bg-purple-900/10 rounded-lg border border-purple-600/20">
                                  <span className="font-bold text-purple-300 text-sm sm:text-base block mb-2">
                                    📄 Job Description:
                                  </span>
                                  <div className="prose prose-sm sm:prose prose-invert max-w-none text-purple-100 [&>*]:text-purple-100 [&>*]:text-xs [&>*]:sm:text-sm [&>*]:leading-relaxed [&>h1]:text-lg [&>h1]:sm:text-xl [&>h1]:font-black [&>h1]:text-purple-300 [&>h1]:border-b-2 [&>h1]:border-purple-500/30 [&>h1]:pb-1 [&>h1]:mb-2 [&>h2]:text-base [&>h2]:sm:text-lg [&>h2]:font-bold [&>h2]:text-purple-400 [&>h2]:border-b [&>h2]:border-purple-600/20 [&>h2]:pb-1 [&>h2]:mb-2 [&>h3]:text-sm [&>h3]:sm:text-base [&>h3]:font-semibold [&>h3]:text-purple-500 [&>h3]:mb-1 [&>h4]:text-sm [&>h4]:font-medium [&>h4]:text-purple-600 [&>h4]:mb-1 [&>p]:text-purple-100 [&>p]:mb-1 [&>p]:break-words [&>ul]:list-disc [&>ul]:list-inside [&>ul]:text-purple-100 [&>ul]:mb-1 [&>ol]:list-decimal [&>ol]:list-inside [&>ol]:text-purple-100 [&>ol]:mb-1 [&>li]:text-purple-100 [&>li]:mb-0.5 [&>li]:break-words [&>strong]:text-purple-300 [&>strong]:font-semibold [&>em]:text-purple-200 [&>em]:italic [&>code]:bg-purple-900/30 [&>code]:text-purple-200 [&>code]:px-1 [&>code]:py-0.5 [&>code]:rounded [&>code]:text-xs [&>blockquote]:border-l-4 [&>blockquote]:border-purple-500/50 [&>blockquote]:pl-2 [&>blockquote]:italic [&>blockquote]:text-purple-200 [&>blockquote]:bg-purple-900/10 [&>blockquote]:py-1 [&>blockquote]:rounded-r">
                                    <ReactMarkdown>
                                      {job_description}
                                    </ReactMarkdown>
                                  </div>
                                </div>
                              )}
                            {/* Other fields */}
                            {otherFields.map(([key]) => {
                              const value = filteredJob[key];
                              if (typeof value === "undefined") {
                                return null;
                              }
                              return (
                                <div key={key} className="mb-1 sm:mb-1.5 p-2 bg-gray-900/10 rounded border border-gray-600/20 break-words">
                                  <span className="font-bold capitalize text-purple-300 text-xs sm:text-sm block mb-1">
                                    {key.replace(/_/g, " ")}:
                                  </span>
                                  {Array.isArray(value) ? (
                                    <span className="text-purple-100 text-xs sm:text-sm">
                                      {value.join(", ")}
                                    </span>
                                  ) : value && typeof value === "object" ? (
                                    <details className="text-xs sm:text-sm">
                                      <summary className="text-purple-200 cursor-pointer hover:text-purple-100">
                                        Show details
                                      </summary>
                                      <pre className="text-purple-100 bg-black/30 rounded p-2 mt-1 overflow-x-auto text-xs break-all">
                                        {JSON.stringify(value, null, 2)}
                                      </pre>
                                    </details>
                                  ) : (
                                    <span className="text-purple-100 text-xs sm:text-sm break-words">
                                      {String(value)}
                                    </span>
                                  )}
                                </div>
                              );
                            })}
                            {/* Extra Sections (job description sections) */}
                            {extra_sections && (
                              <div className="mt-3 sm:mt-4 lg:mt-6">
                                <h3 className="text-base sm:text-lg lg:text-xl font-bold text-purple-400 mb-2 sm:mb-3 lg:mb-4 border-b-2 border-purple-500/30 pb-1 sm:pb-2">
                                  📋 Job Description Sections
                                </h3>
                                <div className="space-y-2 sm:space-y-3 lg:space-y-4">
                                  {Object.entries(extra_sections).map(
                                    ([section, content], sectionIdx) => (
                                      <div
                                        key={section}
                                        className="p-2 sm:p-3 lg:p-4 rounded-lg bg-gradient-to-br from-purple-900/30 to-purple-800/20 border border-purple-700/40 shadow-lg hover:shadow-purple-500/10 transition-all duration-300 hover:scale-[1.01] sm:hover:scale-[1.02]"
                                        style={{
                                          animationDelay: `${
                                            sectionIdx * 50
                                          }ms`,
                                        }}
                                      >
                                        <div className="font-bold text-purple-300 mb-2 sm:mb-3 capitalize text-sm sm:text-base lg:text-lg border-b border-purple-600/30 pb-1 break-words">
                                          {section.replace(/_/g, " ")}
                                        </div>
                                        {Array.isArray(content) ? (
                                          <ul className="list-disc list-inside text-purple-100 space-y-0.5 sm:space-y-1">
                                            {content.map((item, idx) => (
                                              <li
                                                key={idx}
                                                className="text-purple-100 hover:text-purple-200 transition-colors duration-200 text-xs sm:text-sm break-words"
                                              >
                                                {item}
                                              </li>
                                            ))}
                                          </ul>
                                        ) : (
                                          <div className="text-purple-100 whitespace-pre-line leading-relaxed text-xs sm:text-sm break-words">
                                            {String(content)}
                                          </div>
                                        )}
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>
                            )}
                          </CardContent>
                        </div>
                      </Card>
                    );
                  })
                ) : results &&
                  Array.isArray(results) &&
                  results.length === 0 ? (
                  <div className="text-purple-200 text-base sm:text-lg lg:text-xl col-span-1 lg:col-span-2 text-center p-4 sm:p-6 lg:p-8 bg-black/20 rounded-xl sm:rounded-2xl border border-purple-500/20">
                    <div className="text-2xl sm:text-3xl lg:text-4xl mb-2 sm:mb-4">🔍</div>
                    <p className="break-words px-2">No jobs found for your search criteria.</p>
                  </div>
                ) : null}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
      <footer className="mt-8 sm:mt-12 lg:mt-16 text-purple-300/60 text-center text-xs sm:text-sm px-4">
        <div className="flex flex-wrap justify-center items-center gap-2 sm:gap-4 mb-2 sm:mb-3">
          <span className="px-2 sm:px-3 py-1 bg-purple-800/30 rounded-full text-xs">
            Next.js
          </span>
          <span className="px-2 sm:px-3 py-1 bg-purple-800/30 rounded-full text-xs">
            Tailwind
          </span>
          <span className="px-2 sm:px-3 py-1 bg-purple-800/30 rounded-full text-xs">
            shadcn/ui
          </span>
        </div>
        <p className="break-words">
          &copy; {new Date().getFullYear()} Job Scraper Portal. Built with ❤️
        </p>
      </footer>
    </GradientBG>
  );
}
