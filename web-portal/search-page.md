{"data": [{"id": "34550285", "score": 16379999, "jobId": 34550285, "kiwiJobId": "*********", "kiwiCompanyId": "960498", "kiwiRecruiterId": "1260818", "title": "Data Scientist, gTech Ads Solutions", "cleanedJobTitle": "data scientist gtech ads solutions", "company": {"companyId": 960649, "name": "Google Inc", "logo": "https://media.monsterindia.com/logos/xeft_googleincinx/jdlogo.gif"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 3}, "maximumExperience": {"years": 9}, "minimumSalary": {"currency": "INR", "absoluteValue": 300000, "absoluteMonthlyValue": 25000}, "maximumSalary": {"currency": "INR", "absoluteValue": 900000, "absoluteMonthlyValue": 75000}, "postedAt": 1745819176000, "createdAt": 1745819176000, "updatedAt": *************, "industries": ["Software"], "functions": ["IT"], "roles": ["Database Administrator (DBA)", "Database Architect/Designer", "Datawarehousing Consultants"], "description": "<p><strong>Role Responsibilities:</strong></p><ul><li>Guide data science aspects of client engagements, focusing on marketing effectiveness and portfolio management.</li><li>Collaborate with customers to define problems, select statistical techniques, and develop modeling frameworks.</li><li>Assess data and model readiness, scaling proof-of-concepts into full solutions.</li><li>Translate data and model insights into actionable recommendations for business processes.</li></ul><p><strong>Job Requirements:</strong></p><ul><li>Bachelor's degree in Statistics, Engineering, Science, or equivalent experience.</li><li>3 years of experience using analytics and coding (Python, R, SQL) to solve product or business problems.</li><li>Experience in deploying digital analytics and measurement solutions.</li><li>Knowledge of statistical algorithms in Marketing Analytics and applying Machine Learning to customer problems.</li></ul>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "189a96ef-457e-11e9-a89e-70106fbef856", "text": "Machine Learning"}, {"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}, {"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}, {"id": "57e3eb70-bab7-463b-b005-3b121ac8046a", "text": "Statistical Analysis"}], "skills": [{"text": "R"}], "recruiterId": 1510572, "companyId": 960649, "designations": ["Database Administrator (DBA)", "Database Architect/Designer", "Datawarehousing Consultants"], "maximumSalaryINRFilter": 900000, "minimumSalaryINRFilter": 300000, "maximumSalaryINRMonthlyFilter": 75000, "minimumSalaryINRMonthlyFilter": 25000, "minimumExperienceFilter": 3, "maximumExperienceFilter": 9, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Google Inc", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "companyLogoUrl": "https://media.monsterindia.com/logos/xeft_googleincinx/jdlogo.gif", "hideCompanyName": 0, "renewalAt": *************, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "recruiterName": "HR ", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 0, "isJdLogo": 0, "isSearchLogo": 0, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": *************, "closedAt": *************, "activeJob": true, "isCJT": 0, "isMicrosite": 0, "redirectStage": 2, "totalApplicants": 81, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 300000, "maximumSalaryFilter": 900000}, {"id": "34564131", "score": 13229999, "jobId": 34564131, "kiwiJobId": "*********", "kiwiCompanyId": "1170493", "kiwiRecruiterId": "1455641", "title": "Data Scientist - AI Solutions For Electrification", "cleanedJobTitle": "data scientist ai solutions electrification", "company": {"companyId": 1070034, "name": "Siemens", "logo": "https://media.monsterindia.com/logos/xeft_Siemensinx/jdlogo.gif"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 4}, "maximumExperience": {"years": 9}, "minimumSalary": {"currency": "INR", "absoluteValue": 500000, "absoluteMonthlyValue": 41667}, "maximumSalary": {"currency": "INR", "absoluteValue": 1050000, "absoluteMonthlyValue": 87500}, "postedAt": 1745906637000, "createdAt": 1745906637000, "updatedAt": *************, "industries": ["Manufacturing"], "functions": ["IT"], "roles": ["Database Administrator (DBA)"], "description": "<p><strong>We are looking for</strong><strong>Senior Software Architect  (Data Scientist  AI Solutions for Electrification)</strong><strong>,</strong><strong></strong></p><p><strong>You'll make an impact by</strong></p><ul><li>Lead the development of Machine Learning (ML), Deep Learning, and Reinforcement Learning solutions from experimentation to production.</li><li>Identify and frame high-impact opportunities for AI/ML across product workflows and operational processes.</li><li>Perform rapid PoCs and feasibility studies to validate new ideas using real-world datasets.</li><li>Define success metrics and develop robust evaluation strategies to measure the effectiveness and accuracy of ML models.</li><li>Collaborate with product managers, software developers, data engineers, and cloud architects to integrate ML models into scalable systems.</li><li>Select appropriate infrastructure based on performance, cost, and latency constraints, ensuring support for online learning, batch inference, and A/B testing.</li><li>Contribute to data strategy, feature engineering, and feedback loops to continuously improve model performance.</li><li>Ensure responsible AI practices, including model transparency, fairness, explainability, and observability.</li></ul><p><strong>Use your skills to move the world forward!</strong></p><ul><li>Master's or Bachelor's degree in Data Science, Computer Science, Statistics, or a related quantitative field.</li><li>10+ years of software engineering experience with minimum 5 years of experience in applying ML and DL to solve real-world problems.</li><li>Strong expertise in supervised, unsupervised, and reinforcement learning techniques.</li><li>Experience building and deploying models using frameworks like scikit-learn, TensorFlow PyTorch, or XGBoost.</li><li>Proficient in Python and data manipulation libraries such as Pandas, NumPy, and SQL.</li><li>Familiarity with cloud-based AI workflows using AWS (Sagemaker, Bedrock) and/or Azure (ML Studio, OpenAI Service).</li><li>Solid understanding of evaluation metrics, bias/variance tradeo_s, and tuning strategies.</li><li>Hands-on experience with scalable data pipelines, streaming data processing, and distributed training setups.</li><li>Awareness of observability and monitoring practices for models in production.</li><li>Experience working with business stakeholders to translate domain problems into data science solutions.</li><li>Previous experience in the Power, Energy, or Electrification sectors is a strong advantage.</li><li>Experience with experiment tracking tools like MLflow or Weights & Biases, and model versioning systems.</li></ul>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "18b040ff-457e-11e9-a89e-70106fbef856", "text": "Data Science"}, {"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}], "skills": [{"text": "ML and DL"}], "recruiterId": 2674170, "companyId": 1070034, "designations": ["Database Administrator (DBA)"], "maximumSalaryINRFilter": 1050000, "minimumSalaryINRFilter": 500000, "maximumSalaryINRMonthlyFilter": 87500, "minimumSalaryINRMonthlyFilter": 41666, "minimumExperienceFilter": 4, "maximumExperienceFilter": 9, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Siemens", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "companyLogoUrl": "https://media.monsterindia.com/logos/xeft_Siemensinx/jdlogo.gif", "hideCompanyName": 0, "renewalAt": *************, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "recruiterName": "HR ", "companyProfile": "At Siemens India, we believe in transforming the everyday through innovation, technology, and sustainability. By pioneering digital transformation with AI, IoT, and automation, we optimize efficiency across industries.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": *************, "closedAt": *************, "activeJob": true, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 113, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 500000, "maximumSalaryFilter": 1050000}, {"id": "34334876", "score": 13229999, "jobId": 34334876, "kiwiJobId": "*********", "kiwiCompanyId": "787376", "kiwiRecruiterId": "1352290", "title": "Data Scientist, ISS", "cleanedJobTitle": "data scientist iss", "company": {"companyId": 787630, "name": "Amazon Development Centre (India) Private Limited", "logo": "https://media.monsterindia.com/logos/xeft_adciplinx/jdlogo.gif"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 3}, "maximumExperience": {"years": 5}, "minimumSalary": {"currency": "INR", "absoluteValue": 300000, "absoluteMonthlyValue": 25000}, "maximumSalary": {"currency": "INR", "absoluteValue": 500000, "absoluteMonthlyValue": 41667}, "postedAt": 1743656828000, "createdAt": 1743656828000, "updatedAt": *************, "industries": ["Software"], "functions": ["Analytics/Business Intelligence"], "roles": ["Other Analytics/Business Intelligence", "Data Analyst"], "description": "<h2>About the job</h2><p><strong>Description</strong></p><p></p><p>At Amazon, we strive to be Earth's most customer-centric company, where customers can find and discover anything they want to buy online. Our mission in International Seller Services (ISS) is to provide technology solutions for improving the seller and customer experience, drive seller compliance, maximize seller success, and improve internal workforce productivity. Team's main focus is to build products that are scalable across different regions of the world, while working in partnership with ISS regional stakeholders and multiple partner teams across Amazon.</p><p></p><p>As a Data Scientist, you will be responsible for modeling complex problems, discovering insights, and building risk algorithms that identify opportunities through statistical models, machine learning, and visualization techniques to improve operational efficiency.</p><p></p><p>As a Data Scientist, you will leverage your expertise in Machine Learning, Natural Language Processing (NLP), and Large Language Models (LLM) to develop innovative solutions for Amazon's ISS team. You'll be responsible for modeling complex problems, building innovative algorithms, and discovering actionable insights through statistical models and visualization techniques to enhance operational efficiency in the e-commerce space. The role combines usage of latest AI technology with practical business applications, requiring someone passionate about transforming the way we interact with technology while delivering measurable impact through advanced analytics and machine learning solutions.</p><p></p><p>You will need to collaborate effectively with business and product leaders within ISS and cross-functional teams to build scalable solutions against high organizational standards. The candidate should be able to apply a breadth of tools, data sources, and Data Science techniques to answer a wide range of high-impact business questions and proactively present new insights in concise and effective manner.</p><p></p><p>The candidate should be an effective communicator capable of independently driving issues to resolution and communicating insights to non-technical audiences. This is a high impact role with goals that directly impacts the bottom line of the business.</p><p></p><p><strong>Responsibilities: - </strong>Analyze terabytes of data to define and deliver on complex analytical deep dives to unlock insights and build scalable solutions through Data Science to ensure security of Amazon's platform and transactions</p><p></p><ul><li> Build Machine Learning and/or statistical models that evaluate the transaction legitimacy and track impact over time</li><li> Ensure data quality throughout all stages of acquisition and processing, including data sourcing/collection, ground truth generation, normalization, transformation, and cross-lingual alignment/mapping</li><li> Define and conduct experiments to validate/reject hypotheses, and communicate insights and recommendations to Product and Tech teams</li><li> Develop efficient data querying infrastructure for both offline and online use cases</li><li> Collaborate with cross-functional teams from multidisciplinary science, engineering and business backgrounds to enhance current automation processes</li><li> Learn and understand a broad range of Amazon's data resources and know when, how, and which to use and which not to use.</li><li> Maintain technical document and communicate results to diverse audiences with effective writing, visualizations, and presentations</li></ul><p></p><p><strong>Basic Qualifications</strong></p><p></p><ul><li> 2+ years of data scientist experience</li><li> 3+ years of data querying languages (e.g. SQL), scripting languages (e.g. Python) or statistical/mathematical software (e.g. R, SAS, Matlab, etc.) experience</li><li> 3+ years of machine learning/statistical modeling data analysis tools and techniques, and parameters that affect their performance experience</li><li> Experience applying theoretical models in an applied environment</li></ul><p></p><p><strong>Preferred Qualifications</strong></p><p></p><ul><li> Experience in Python, Perl, or another scripting language</li><li> Experience in a ML or data scientist role with a large technology company</li></ul><p></p><p>Our inclusive culture empowers Amazonians to deliver the best results for our customers. If you have a disability and need a workplace accommodation or adjustment during the application and hiring process, including support for the interview or onboarding process, please visit <a href=\"https://amazon.jobs/content/en/how-we-hire/accommodations\" rel=\"noopener noreferrer\" target=\"_blank\">https://amazon.jobs/content/en/how-we-hire/accommodations</a> for more information. If the country/region you're applying in isn't listed, please contact your Recruiting Partner.</p><p></p><p>Company - ADCI HYD 13 SEZ</p>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "189a96ef-457e-11e9-a89e-70106fbef856", "text": "Machine Learning"}, {"id": "18b040ff-457e-11e9-a89e-70106fbef856", "text": "Data Science"}, {"id": "e2189b50-1d1d-4d78-a605-67369d56d1f9", "text": "Data Scientist"}, {"id": "5c4e3b37-3a60-49ab-a2e7-d1b84a28777c", "text": "Data Science - ML"}], "recruiterId": 1876024, "companyId": 787630, "designations": ["Other Analytics/Business Intelligence", "Data Analyst"], "maximumSalaryINRFilter": 500000, "minimumSalaryINRFilter": 300000, "maximumSalaryINRMonthlyFilter": 41666, "minimumSalaryINRMonthlyFilter": 25000, "minimumExperienceFilter": 3, "maximumExperienceFilter": 5, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Amazon Development Centre (India) Private Limited", "referenceCode": "********", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "companyLogoUrl": "https://media.monsterindia.com/logos/xeft_adciplinx/jdlogo.gif", "hideCompanyName": 0, "renewalAt": *************, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "Amazon Development Centre (India) Private Limited", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": *************, "closedAt": *************, "activeJob": true, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 203, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 300000, "maximumSalaryFilter": 500000}, {"id": "35486381", "score": 12599999, "jobId": 35486381, "kiwiJobId": "*********", "kiwiCompanyId": "1245519", "kiwiRecruiterId": "1526915", "title": "Data Scientist", "cleanedJobTitle": "data scientist", "company": {"companyId": 1119300, "name": "Fusion Plus Solutions"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 4}, "maximumExperience": {"years": 8}, "minimumSalary": {"currency": "INR", "absoluteValue": 350000, "absoluteMonthlyValue": 29167}, "maximumSalary": {"currency": "INR", "absoluteValue": 1200000, "absoluteMonthlyValue": 100000}, "postedAt": 1752753000000, "createdAt": 1752753000000, "updatedAt": 1752752999000, "industries": ["Information Technology"], "functions": ["Analytics/Business Intelligence"], "roles": ["Data Analyst"], "description": "<p><strong>Responsibilities</strong></p><ul><li>Analyze large and complex datasets to uncover insights, trends, and patterns</li><li>Build predictive models and machine learning algorithms to solve business problems</li><li>Clean, preprocess, and validate data for accurate analysis and modeling</li><li>Develop data-driven solutions and provide actionable recommendations to stakeholders</li><li>Collaborate with cross-functional teams to understand business objectives and translate them into data science projects</li><li>Visualize data insights using dashboards, reports, and storytelling techniques</li><li>Evaluate model performance and fine-tune for accuracy and efficiency</li><li>Stay up-to-date with the latest developments in data science, machine learning, and AI technologies</li></ul><p></p>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}, {"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}, {"id": "189a96ef-457e-11e9-a89e-70106fbef856", "text": "Machine Learning"}, {"id": "190049e2-457e-11e9-a89e-70106fbef856", "text": "Deep Learning"}, {"id": "bdf86e4a-7571-4798-a5d7-e2aa1392dc34", "text": "Data Mining"}, {"id": "18eb9da5-457e-11e9-a89e-70106fbef856", "text": "Data Visualization"}], "skills": [{"text": "R"}, {"id": "64c88cde-4a0b-11e9-a89e-70106fbef856", "text": "Statistics"}], "recruiterId": 3478963, "companyId": 1119300, "designations": ["Data Analyst"], "maximumSalaryINRFilter": 1200000, "minimumSalaryINRFilter": 350000, "maximumSalaryINRMonthlyFilter": 100000, "minimumSalaryINRMonthlyFilter": 29166, "minimumExperienceFilter": 4, "maximumExperienceFilter": 8, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Fusion Plus Solutions", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "hideCompanyName": 0, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "At Fusion Plus Solutions Inc, we believe that it&rsquo;s an exceptional company - a company of people proud of the work they do and the solutions they provide. By understanding what drives our specialty industries, becoming involved in our communities on a professional and personal basis, following a disciplined process of identifying quality candidates, partnering with employers to understand their core business and their employment requirements, and delivering exceptional service, we achieve great results for all concerned.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": 1752753000000, "closedAt": 1757874600000, "activeJob": true, "earlyApplicant": false, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 44, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 350000, "maximumSalaryFilter": 1200000}, {"id": "34522555", "score": 12599999, "jobId": 34522555, "kiwiJobId": "*********", "kiwiCompanyId": "1133454", "kiwiRecruiterId": "1421292", "title": "Data Scientist", "cleanedJobTitle": "data scientist", "company": {"companyId": 1069710, "name": "PHOTON", "logo": "https://media.monsterindia.com/logos/xeft_photoninx/jdlogo.gif"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}, {"city": "Chennai", "country": "India", "state": "Tamil Nadu", "latLon": "13.082680,80.270718", "uuid": "b2b9a668-5339-4ea8-8b02-2f7e1a582556", "isoCode": "IN", "isdCode": 91, "stdCode": 44}, {"city": "Pune", "country": "India", "state": "Maharashtra", "latLon": "18.520430,73.856744", "uuid": "0fb3f865-7da3-424a-8f14-a0d9db705ec1", "isoCode": "IN", "isdCode": 91, "stdCode": 22}], "minimumExperience": {"years": 8}, "maximumExperience": {"years": 12}, "minimumSalary": {"currency": "INR", "absoluteValue": 800000, "absoluteMonthlyValue": 66667}, "maximumSalary": {"currency": "INR", "absoluteValue": 1200000, "absoluteMonthlyValue": 100000}, "postedAt": 1745582455000, "createdAt": 1745582455000, "updatedAt": *************, "industries": ["Consulting", "Information Services"], "functions": ["Analytics/Business Intelligence"], "roles": ["Data Analyst"], "description": "<p><strong>Role Overview:</strong>We are seeking a<strong>Senior Machine Learning Software Engineer</strong>to join our team and drive the development of<strong>cutting-edge AI/ML solutions</strong>. You will play a key role in<strong>designing, building, and optimizing</strong>our<strong>machine learning infrastructure</strong>while collaborating with<strong>Product Managers, Data Scientists, and Engineers</strong>to bring innovative models into production. The ideal candidate should have a strong background in<strong>ML operations, software engineering, and automation</strong>to ensure a scalable and robust AI-driven ecosystem.</p><p></p><p><strong>Key Responsibilities:Operationalize</strong>leading<strong>data and analytics technologies</strong>while following best practices.</p><ul><li>Partner with<strong>Product Managers</strong>to understand customer requirements,<strong>design prototypes, and deploy solutions to production</strong>.</li></ul><p></p><p><strong>Design, build, harden, and maintain</strong>the core infrastructure supporting the<strong>ML platform and lifecycle</strong>.</p><ul><li>Define and contribute to the<strong>technology strategy, roadmap, and priorities</strong>for AI/ML initiatives.</li><li>Automate every aspect of the<strong>ML infrastructure</strong>to<strong>minimize human intervention</strong>and improve efficiency.</li><li>Encourage and<strong>implement best practices</strong>in<strong>data engineering and software development</strong>.</li><li>Write<strong>high-quality, production-grade code</strong>that aligns with company standards and contribute to<strong>new standardization efforts</strong>.</li><li>Implement<strong>automated testing</strong>to ensure reliability and correctness of ML models and infrastructure.</li><li><strong>Review and mentor</strong>other engineers to promote high-quality engineering practices.</li><li>Actively participate in<strong>technical communities-of-practice</strong>and<strong>continuing education programs</strong>to advance personal and organizational growth.</li><li>Facilitate<strong>problem diagnosis and resolution</strong>across<strong>technical and functional</strong>areas.</li></ul><p></p><p><strong>Required Skills & Competencies:8+ years</strong>of experience in<strong>software engineering, machine learning, or data engineering</strong>.</p><ul><li>Expertise in<strong>designing, coding, and scripting</strong>to develop<strong>real-world AI/ML solutions</strong>.</li><li>Strong programming skills in<strong>Python, Java, or Scala</strong>with hands-on experience in<strong>ML frameworks.</strong></li><li>Experience with<strong>ML model deployment, MLOps, and automation</strong>.</li><li>Proficiency in<strong>cloud-based ML solutions.</strong></li><li>Knowledge of<strong>data pipeline orchestration tools.</strong></li><li>Strong background in<strong>data engineering concepts.</strong></li><li>Experience working with<strong>containerization.</strong></li><li>Strong analytical and problem-solving skills with a<strong>collaborative mindset</strong>.</li><li>Excellent<strong>communication and leadership skills</strong>with the ability to<strong>mentor and guide</strong>junior engineers.</li></ul><p></p>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "8997ffdb-8967-49ed-8b2b-df6a17f05b48", "text": "Java"}, {"id": "8e7347b0-5558-4158-983a-4358ec52fc47", "text": "Software Engineering"}, {"id": "5f5af57b-6bbd-439b-bf02-4c5cf3e94150", "text": "MLops"}, {"id": "18b17dac-457e-11e9-a89e-70106fbef856", "text": "Scala"}, {"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}, {"id": "18b040ff-457e-11e9-a89e-70106fbef856", "text": "Data Science"}, {"id": "18b5b1d0-457e-11e9-a89e-70106fbef856", "text": "Coding"}], "recruiterId": 2610911, "companyId": 1069710, "designations": ["Data Analyst"], "maximumSalaryINRFilter": 1200000, "minimumSalaryINRFilter": 800000, "maximumSalaryINRMonthlyFilter": 100000, "minimumSalaryINRMonthlyFilter": 66666, "minimumExperienceFilter": 8, "maximumExperienceFilter": 12, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "PHOTON", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "companyLogoUrl": "https://media.monsterindia.com/logos/xeft_photoninx/jdlogo.gif", "hideCompanyName": 0, "renewalAt": *************, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "Photon, a global leader in AI and digital solutions, helps clients accelerate AI adoption and embrace Digital Hyper-expansion&reg; to &lsquo;make tomorrow happen today&rsquo;. We work with 40% of the Fortune 100, enabling them to stay agile and future-ready in an era of converging digital and AI boundaries. Powering billions of touch points a day, Photon combines AI management, digital innovation, product design thinking, and engineering excellence to drive lasting transformation for F500 clients. We employ several thousand people across dozens of countries.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": *************, "closedAt": *************, "activeJob": true, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 40, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 800000, "maximumSalaryFilter": 1200000}, {"id": "35536382", "score": 12599999, "jobId": 35536382, "kiwiJobId": "*********", "kiwiCompanyId": "1245519", "kiwiRecruiterId": "1526915", "title": "Subcon demand For Apple project - Data scientist", "cleanedJobTitle": "subcon demand apple project data scientist", "company": {"companyId": 1119300, "name": "Fusion Plus Solutions"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 3}, "maximumExperience": {"years": 6}, "minimumSalary": {"currency": "INR", "absoluteValue": 400000, "absoluteMonthlyValue": 33333}, "maximumSalary": {"currency": "INR", "absoluteValue": 600000, "absoluteMonthlyValue": 50000}, "postedAt": 1753091293000, "createdAt": 1753091293000, "updatedAt": 1753091293000, "industries": ["Information Technology"], "functions": ["IT"], "roles": ["Software Engineer/Programmer"], "description": "<ul><li>3+ years of hands-on experience with NLP projects and ML models</li><li>Strong proficiency in SQL and experience querying large datasets</li><li>Practical experience with Apache Spark and distributed computing</li><li>Proven track record implementing NLP solutions (text classification, sentiment analysis, entity recognition)</li><li>Python programming skills with NLP libraries (NLTK, spaCy, transformers)</li></ul><p><strong>Key Responsibilities:</strong></p><ul><li>Design and implement NLP solutions for text analysis and processing</li><li>Build and optimize data pipelines for processing large-scale text data</li><li>Develop and maintain ML models for text classification and extraction</li><li>Collaborate with engineering teams to productionize NLP models</li><li>Drive insights through analysis of unstructured text data</li></ul><p></p>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "25b19ee2-457e-11e9-a89e-70106fbef856", "text": "Nltk"}, {"id": "191083e8-457e-11e9-a89e-70106fbef856", "text": "Nlp"}, {"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}, {"id": "1c7b89ef-457e-11e9-a89e-70106fbef856", "text": "Ml"}], "skills": [{"text": "spaCy"}], "recruiterId": 3478963, "companyId": 1119300, "designations": ["Software Engineer/Programmer"], "maximumSalaryINRFilter": 600000, "minimumSalaryINRFilter": 400000, "maximumSalaryINRMonthlyFilter": 50000, "minimumSalaryINRMonthlyFilter": 33333, "minimumExperienceFilter": 3, "maximumExperienceFilter": 6, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Fusion Plus Solutions", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "hideCompanyName": 0, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "At Fusion Plus Solutions Inc, we believe that it&rsquo;s an exceptional company - a company of people proud of the work they do and the solutions they provide. By understanding what drives our specialty industries, becoming involved in our communities on a professional and personal basis, following a disciplined process of identifying quality candidates, partnering with employers to understand their core business and their employment requirements, and delivering exceptional service, we achieve great results for all concerned.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": 1753091293000, "closedAt": 1758220200000, "activeJob": true, "earlyApplicant": false, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 39, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 400000, "maximumSalaryFilter": 600000}, {"id": "34600990", "score": 12599999, "jobId": 34600990, "kiwiJobId": "*********", "kiwiCompanyId": "1133454", "kiwiRecruiterId": "1421292", "title": "Data Scientist - BLR", "cleanedJobTitle": "data scientist blr", "company": {"companyId": 1069710, "name": "PHOTON", "logo": "https://media.monsterindia.com/logos/xeft_photoninx/jdlogo.gif"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}, {"city": "Bengaluru / Bangalore", "country": "India", "state": "Karnataka", "latLon": "12.971599,77.594563", "uuid": "67bfdcae-92cb-4783-bf40-37f639e57710", "isoCode": "IN", "isdCode": 91, "stdCode": 80}, {"city": "Chennai", "country": "India", "state": "Tamil Nadu", "latLon": "13.082680,80.270718", "uuid": "b2b9a668-5339-4ea8-8b02-2f7e1a582556", "isoCode": "IN", "isdCode": 91, "stdCode": 44}], "minimumExperience": {"years": 6}, "maximumExperience": {"years": 9}, "minimumSalary": {"currency": "INR", "absoluteValue": 600000, "absoluteMonthlyValue": 50000}, "maximumSalary": {"currency": "INR", "absoluteValue": 900000, "absoluteMonthlyValue": 75000}, "postedAt": 1746011644000, "createdAt": 1746011644000, "updatedAt": *************, "industries": ["Information Technology"], "functions": ["Analytics/Business Intelligence", "IT"], "roles": ["Software Engineer/Programmer", "Business Analyst", "Data Analyst"], "description": "<ul><li>Photon Infotech P Ltd is looking for Data Scientist - B<PERSON> to join our dynamic team and embark on a rewarding career journey.</li><li>Undertaking data collection, preprocessing and analysis</li><li>Building models to address business problems</li><li>Presenting information using data visualization techniques</li><li>Identify valuable data sources and automate collection processes</li><li>Undertake preprocessing of structured and unstructured data</li><li>Analyze large amounts of information to discover trends and patterns</li><li>Build predictive models and machine-learning algorithms</li><li>Combine models through ensemble modeling</li><li>Present information using data visualization techniques</li><li>Propose solutions and strategies to business challenges</li><li>Collaborate with engineering and product development teams</li></ul><p><strong>Role:</strong><a href=\"https://www.naukri.com/data-scientist-jobs\" rel=\"noopener noreferrer\" target=\"_blank\">Data Scientist</a></p><p><strong>Industry Type:</strong><a href=\"https://www.naukri.com/it-services-consulting-jobs\" rel=\"noopener noreferrer\" target=\"_blank\">IT Services & Consulting</a></p><p><strong>Department:</strong><a href=\"https://www.naukri.com/data-science-analytics-jobs\" rel=\"noopener noreferrer\" target=\"_blank\">Data Science & Analytics</a></p><p><strong>Employment Type:</strong>Full Time, Permanent</p><p><strong>Role Category:</strong>Data Science & Machine Learning</p><p><strong>Education</strong></p><p><strong>UG:</strong>Any Graduate</p><p><strong>PG:</strong>Any Postgraduate</p>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "54d777ae-6757-4c55-b7ab-85b8e5f84510", "text": "Algorithms"}, {"id": "189a96ef-457e-11e9-a89e-70106fbef856", "text": "Machine Learning"}, {"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "python"}, {"id": "18b040ff-457e-11e9-a89e-70106fbef856", "text": "Data Science"}, {"id": "bdf86e4a-7571-4798-a5d7-e2aa1392dc34", "text": "data mining"}, {"id": "18f0d06a-457e-11e9-a89e-70106fbef856", "text": "Predictive Modeling"}], "skills": [{"id": "7433a1a7-5462-11e9-a89e-70106fbef856", "text": "Predictive"}], "recruiterId": 2610911, "companyId": 1069710, "designations": ["Software Engineer/Programmer", "Business Analyst", "Data Analyst"], "maximumSalaryINRFilter": 900000, "minimumSalaryINRFilter": 600000, "maximumSalaryINRMonthlyFilter": 75000, "minimumSalaryINRMonthlyFilter": 50000, "minimumExperienceFilter": 6, "maximumExperienceFilter": 9, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "PHOTON", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "companyLogoUrl": "https://media.monsterindia.com/logos/xeft_photoninx/jdlogo.gif", "hideCompanyName": 0, "renewalAt": *************, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "Photon, a global leader in AI and digital solutions, helps clients accelerate AI adoption and embrace Digital Hyper-expansion&reg; to &lsquo;make tomorrow happen today&rsquo;. We work with 40% of the Fortune 100, enabling them to stay agile and future-ready in an era of converging digital and AI boundaries. Powering billions of touch points a day, Photon combines AI management, digital innovation, product design thinking, and engineering excellence to drive lasting transformation for F500 clients. We employ several thousand people across dozens of countries.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": *************, "closedAt": *************, "activeJob": true, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 41, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 600000, "maximumSalaryFilter": 900000}, {"id": "35491341", "score": 12000000, "jobId": 35491341, "kiwiJobId": "*********", "kiwiCompanyId": "3709", "kiwiRecruiterId": "1240667", "title": "Data Scientist- ( Lead)", "cleanedJobTitle": "data scientist lead", "company": {"companyId": 3709, "name": "Sampoorna Consultants Private Limited", "logo": "https://media.monsterindia.com/logos/xsampoornainx/jdlogo.gif"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 0}, "maximumExperience": {"years": 0}, "minimumSalary": {"currency": "INR", "absoluteValue": 0, "absoluteMonthlyValue": 0}, "maximumSalary": {"currency": "INR", "absoluteValue": 0, "absoluteMonthlyValue": 0}, "postedAt": 1752863916000, "createdAt": 1752863916000, "updatedAt": 1752863915000, "industries": ["Recruitment/Staffing/RPO"], "functions": ["Data Science"], "description": "<td></td><td><div><div></div><br/><div><div>we are driven to deliver service excellence and quality is the center of every project. With team of more than 75 eminent data scientists, Our client provides advanced AI-based customized solutions, big data infrastructure development and integration services, predictive analytics and machine learning technologies, and corporate training in big data analytics. Typically, our project life cycle entails: <br/> We follow comprehensive testing before the solution is deployed in the production environment. There are components of solution which ensure that identified stakeholders are alerted when system reports a confidence level lower than benchmark. These alerts can be sent via email or other real-time notifications. </div><br/></div></div></td><td></td><td><div><div></div><br/><div><div> Proficiency with Python (Pandas, NumPy), SQL, and Java.<br/> Experience with LLMs, LangChain, and Generative AI technologies.<br/> Familiarity with ML frameworks (TensorFlow, PyTorch) and data engineering tools (Spark, Kafka).<br/> Microservices, CI CD, ML<br/> Strong data analysis skills and ability to present findings to both technical and non-technical stakeholders.<br/> Proficient understanding of key data engineering concepts, such as data lakes, columnar formats, ETL tools, and BI tools.<br/> Knowledge in Machine Learning, NLP, Recommender systems, personalization, Segmentation, microservices architecture and API development.<br/> Ability to adapt to a fast-paced, dynamic work environment and learn new technologies quickly.<br/><br/> Work in a team/ Independently.<br/> Excellent Written & Verbal Communication Skills<br/> Solid critical thinking and questioning skills.<br/> High degree of flexibility - willing to fill in the gaps rather than relying on others<br/> Strong communication skills, especially in presenting data insights.<br/> Flexibility, problem-solving, and a proactive approach in a fast-paced environment<br/></div><br/></div></div></td>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "8997ffdb-8967-49ed-8b2b-df6a17f05b48", "text": "Java"}, {"id": "189a96ef-457e-11e9-a89e-70106fbef856", "text": "Machine Learning"}, {"id": "27b9f578-457e-11e9-a89e-70106fbef856", "text": "Personalization"}, {"id": "1abb8bfa-457e-11e9-a89e-70106fbef856", "text": "Api Development"}, {"id": "18ce29fe-457e-11e9-a89e-70106fbef856", "text": "Kafka"}, {"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}, {"id": "18dea694-457e-11e9-a89e-70106fbef856", "text": "Microservices"}, {"id": "1b4f8a41-457e-11e9-a89e-70106fbef856", "text": "Tensorflow"}, {"id": "1b96cd2d-457e-11e9-a89e-70106fbef856", "text": "<PERSON><PERSON><PERSON>"}, {"id": "191083e8-457e-11e9-a89e-70106fbef856", "text": "Nlp"}, {"id": "1b2c0640-457e-11e9-a89e-70106fbef856", "text": "<PERSON><PERSON>"}, {"id": "278aa4d5-457e-11e9-a89e-70106fbef856", "text": "Pytor<PERSON>"}, {"id": "18a5f232-457e-11e9-a89e-70106fbef856", "text": "Spark"}, {"id": "25fc6609-457e-11e9-a89e-70106fbef856", "text": "Recommender Systems"}, {"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}], "skills": [{"text": "<PERSON><PERSON><PERSON><PERSON>"}, {"text": "Generative AI"}, {"text": "LLMs"}, {"id": "7581e012-5462-11e9-a89e-70106fbef856", "text": "Segmentation"}, {"text": "CI CD"}], "recruiterId": 1478127, "companyId": 3709, "maximumSalaryINRFilter": 0, "minimumSalaryINRFilter": 0, "maximumSalaryINRMonthlyFilter": 0, "minimumSalaryINRMonthlyFilter": 0, "minimumExperienceFilter": 0, "maximumExperienceFilter": 0, "questionnaire": false, "employerTypes": ["Consultant"], "walkInVenue": {}, "companyName": "Sampoorna Consultants Private Limited", "referenceCode": "a440cac7c0c4a57bca5453166aa19b01", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 0, "companyLogoUrl": "https://media.monsterindia.com/logos/xsampoornainx/jdlogo.gif", "hideCompanyName": 0, "recruiterContactNumber": "**********", "showContactDetails": 0, "jobSource": "SCRAPPING", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "recruiterName": "Sampoorna Consultants Private Limited", "companyProfile": "&amp;#x22;Sampoorna Computer People is a Recruitment Agency established in 1990, providing consultancy exclusively in recruitment of IT, ITES (BPO) &amp;#x26; Telecom Professionals. Our Head Office is in Mumbai (Bombay) and we have offices at Bangalore, Chennai (Madras), Delhi, Hyderabad and Pune. We are the first IT &amp;#x26; Telecom specialist recruitment consultant certified for ISO 9001:2015 by BVQI India. <PERSON><PERSON><PERSON><PERSON> is a founder member of ERA (Executive Recruiters Association) which is India&acirc;&euro;&trade;s Recruitment Agency&acirc;&euro;&trade;s Industry Organization Home page : www.sampoorna.com&amp;#x22;<br>", "channelId": 1, "channelName": "India", "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": 1752863916000, "closedAt": 1756751400000, "activeJob": true, "isCJT": 0, "isMicrosite": 1, "redirectStage": 2, "totalApplicants": 199, "isGptwCompany": 0, "jobSalaryConfidential": false, "isUrgentlyHiring": false, "quickApplyJob": 0, "jobClassification": "SYNDICATE_EC", "isMarquee": 0, "isTestJob": false, "minimumSalaryFilter": 0, "maximumSalaryFilter": 0}, {"id": "35493110", "score": 12000000, "jobId": 35493110, "kiwiJobId": "*********", "kiwiCompanyId": "3709", "kiwiRecruiterId": "1240667", "title": "Gen AI | ML- Data Scientist- ( Lead) -Mum/chennai", "cleanedJobTitle": "gen ai ml data scientist lead -mum", "company": {"companyId": 3709, "name": "Sampoorna Consultants Private Limited", "logo": "https://media.monsterindia.com/logos/xsampoornainx/jdlogo.gif"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 0}, "maximumExperience": {"years": 0}, "minimumSalary": {"currency": "INR", "absoluteValue": 0, "absoluteMonthlyValue": 0}, "maximumSalary": {"currency": "INR", "absoluteValue": 0, "absoluteMonthlyValue": 0}, "postedAt": 1752950320000, "createdAt": 1752950320000, "updatedAt": 1753295746000, "industries": ["Recruitment/Staffing/RPO"], "functions": ["Data Science And Engineering"], "description": "<td></td><td><div><div></div><br/><div><div>we are driven to deliver service excellence and quality is the center of every project. With team of more than 75 eminent data scientists, Our client provides advanced AI-based customized solutions, big data infrastructure development and integration services, predictive analytics and machine learning technologies, and corporate training in big data analytics. Typically, our project life cycle entails: <br/> We follow comprehensive testing before the solution is deployed in the production environment. There are components of solution which ensure that identified stakeholders are alerted when system reports a confidence level lower than benchmark. These alerts can be sent via email or other real-time notifications. </div><br/></div></div></td><td></td><td><div><div></div><br/><div><div> This role is responsible for managing the client expectations. Strategize with various stakeholders to meet customer requirements. <br/>KEY RESPONSIBILITIES<br/> Data Science: Develop machine learning models to support recommendation systems and NLP projects provide actionable insights for product and service optimization.<br/> Data Engineering: Build and maintain scalable ETL pipelines, optimize data storage solutions (data lakes, columnar formats), and ensure data accuracy for analytics.<br/> Data Analysis and Insight Generation: Skilled in analyzing complex datasets to uncover trends and patterns generate and present insights that drive strategic decisions and enhance client services.<br/> Stakeholder Collaboration: Work with product and service teams to understand data needs and translate them into technical solutions. <br/><br/>Working Relationships<br/>Reporting to Project Manager<br/>External Stakeholders Clients<br/><br/>Skills/ Competencies Required <br>Technical Skills Proficiency with Python (Pandas, NumPy), SQL, and Java.<br> Experience with LLMs, LangChain, and Generative AI technologies.<br/> Familiarity with ML frameworks (TensorFlow, PyTorch) and data engineering tools (Spark, Kafka).<br/> Microservices, CI CD, ML<br/> Strong data analysis skills and ability to present findings to both technical and non-technical stakeholders.<br/> Proficient understanding of key data engineering concepts, such as data lakes, columnar formats, ETL tools, and BI tools.<br/> Knowledge in Machine Learning, NLP, Recommender systems, personalization, Segmentation, microservices architecture and API development.<br/> Ability to adapt to a fast-paced, dynamic work environment and learn new technologies quickly.<br/><br/>Soft Skills Work in a team/ Independently. <br/> Excellent Written & Verbal Communication Skills<br/> Solid critical thinking and questioning skills.<br/> High degree of flexibility - willing to fill in the gaps rather than relying on others<br/> Strong communication skills, especially in presenting data insights.<br/> Flexibility, problem-solving, and a proactive approach in a fast-paced environment<br/></br></br></div><br/></div></div></td>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "recruiterId": 1478127, "companyId": 3709, "designations": ["Software Engineer/Programmer"], "maximumSalaryINRFilter": 0, "minimumSalaryINRFilter": 0, "maximumSalaryINRMonthlyFilter": 0, "minimumSalaryINRMonthlyFilter": 0, "minimumExperienceFilter": 0, "maximumExperienceFilter": 0, "questionnaire": false, "employerTypes": ["Consultant"], "walkInVenue": {}, "companyName": "Sampoorna Consultants Private Limited", "referenceCode": "510ae59d819729b863f831d746efa8ae", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 2, "companyLogoUrl": "https://media.monsterindia.com/logos/xsampoornainx/jdlogo.gif", "hideCompanyName": 0, "recruiterContactNumber": "**********", "showContactDetails": 0, "jobSource": "SCRAPPING", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "recruiterName": "Sampoorna Consultants Private Limited", "companyProfile": "&amp;#x22;Sampoorna Computer People is a Recruitment Agency established in 1990, providing consultancy exclusively in recruitment of IT, ITES (BPO) &amp;#x26; Telecom Professionals. Our Head Office is in Mumbai (Bombay) and we have offices at Bangalore, Chennai (Madras), Delhi, Hyderabad and Pune. We are the first IT &amp;#x26; Telecom specialist recruitment consultant certified for ISO 9001:2015 by BVQI India. <PERSON><PERSON><PERSON><PERSON> is a founder member of ERA (Executive Recruiters Association) which is India&acirc;&euro;&trade;s Recruitment Agency&acirc;&euro;&trade;s Industry Organization Home page : www.sampoorna.com&amp;#x22;<br>", "channelId": 1, "channelName": "India", "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": 1752950320000, "closedAt": 1756837800000, "activeJob": true, "isCJT": 0, "isMicrosite": 1, "redirectStage": 2, "totalApplicants": 182, "isGptwCompany": 0, "jobSalaryConfidential": false, "isUrgentlyHiring": false, "quickApplyJob": 0, "jobClassification": "SYNDICATE_EC", "isMarquee": 0, "isTestJob": false, "minimumSalaryFilter": 0, "maximumSalaryFilter": 0}, {"id": "35479004", "score": 11906999, "jobId": 35479004, "kiwiJobId": "*********", "kiwiCompanyId": "1245121", "kiwiRecruiterId": "1526525", "title": "Data Scientist 2", "cleanedJobTitle": "data scientist 2", "company": {"companyId": 1118906, "name": "Uber"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 9}, "maximumExperience": {"years": 10}, "minimumSalary": {"currency": "INR", "absoluteValue": 350000, "absoluteMonthlyValue": 29167}, "maximumSalary": {"currency": "INR", "absoluteValue": 1400000, "absoluteMonthlyValue": 116667}, "postedAt": 1752494685000, "createdAt": 1752494685000, "updatedAt": 1752494685000, "industries": ["Transportation", "Information Services"], "functions": ["Analytics/Business Intelligence"], "roles": ["Data Analyst"], "description": "<h2><strong>Description</strong></h2><p>We are seeking a skilled Data Scientist 2 to join our team in India. The ideal candidate will have 9-10 years of experience in data science and analytics, with a proven track record of deriving insights from complex datasets and delivering impactful solutions to business challenges.</p><p></p><h2><strong>Responsibilities</strong></h2><ul><li>Analyze large datasets to derive actionable insights.</li><li>Develop and implement data models and algorithms to solve business problems.</li><li>Collaborate with cross-functional teams to understand their data needs and provide solutions.</li><li>Communicate findings and recommendations to stakeholders through presentations and reports.</li><li>Continuously improve data quality and processes by identifying areas for enhancement.</li><li>Stay updated with the latest trends and technologies in data science and analytics.</li></ul><p></p><h2><strong>Skills and Qualifications</strong></h2><ul><li>Proficiency in programming languages such as Python, R, or Scala.</li><li>Strong understanding of machine learning algorithms and statistical modeling.</li><li>Experience with data visualization tools such as Tableau, Power BI, or Matplotlib.</li><li>Knowledge of big data technologies like Hadoop, Spark, or similar frameworks.</li><li>Ability to work with databases using SQL and NoSQL solutions.</li><li>Strong analytical and problem-solving skills with attention to detail.</li><li>Excellent communication skills to convey complex information clearly and effectively.</li></ul>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "189a96ef-457e-11e9-a89e-70106fbef856", "text": "Machine Learning"}, {"id": "1c876dee-457e-11e9-a89e-70106fbef856", "text": "Python Programming"}, {"id": "18eb9da5-457e-11e9-a89e-70106fbef856", "text": "Data Visualization"}, {"id": "7621f192-42ac-4dd0-8d45-c6cc54ae902e", "text": "Big Data"}, {"id": "bdf86e4a-7571-4798-a5d7-e2aa1392dc34", "text": "Data Mining"}, {"id": "57e3eb70-bab7-463b-b005-3b121ac8046a", "text": "Statistical Analysis"}], "skills": [{"text": "SQL Databases"}], "recruiterId": 3478550, "companyId": 1118906, "designations": ["Data Analyst"], "maximumSalaryINRFilter": 1400000, "minimumSalaryINRFilter": 350000, "maximumSalaryINRMonthlyFilter": 116666, "minimumSalaryINRMonthlyFilter": 29166, "minimumExperienceFilter": 9, "maximumExperienceFilter": 10, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Uber", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "hideCompanyName": 0, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "We are Uber. The go-getters. The kind of people who are relentless about our mission to help people go anywhere and get anything and earn their way. Movement is what we power. It&rsquo;s our lifeblood. It runs through our veins. It&rsquo;s what gets us out of bed each morning. It pushes us to constantly reimagine how we can move better. For you. For all the places you want to go. For all the things you want to get. For all the ways you want to earn. Across the entire world. In real time. At the incredible speed of now. The idea for Uber was born on a snowy night in Paris in 2008, and ever since then our DNA of reimagination and reinvention carries on. We&rsquo;ve grown into a global platform powering flexible earnings and the movement of people and things in ever expanding ways. We&rsquo;ve gone from connecting rides on 4 wheels to 2 wheels to 18-wheel freight deliveries. From takeout meals to daily essentials to prescription drugs to just about anything you need at any time and earning your way. From drivers with background checks to real-time verification, safety is a top priority every single day. At Uber, the pursuit of reimagination is never finished, never stops, and is always just beginning.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": 1752494685000, "closedAt": 1757615400000, "activeJob": true, "earlyApplicant": false, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 38, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 350000, "maximumSalaryFilter": 1400000}, {"id": "35478817", "score": 11906999, "jobId": 35478817, "kiwiJobId": "*********", "kiwiCompanyId": "1245121", "kiwiRecruiterId": "1526525", "title": "Data Scientist I", "cleanedJobTitle": "data scientist i", "company": {"companyId": 1118906, "name": "Uber"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 3}, "maximumExperience": {"years": 6}, "minimumSalary": {"currency": "INR", "absoluteValue": 250000, "absoluteMonthlyValue": 20833}, "maximumSalary": {"currency": "INR", "absoluteValue": 450000, "absoluteMonthlyValue": 37500}, "postedAt": 1752488420000, "createdAt": 1752488420000, "updatedAt": 1752488419000, "industries": ["Transportation", "Information Services"], "functions": ["Analytics/Business Intelligence"], "roles": ["Data Analyst"], "description": "<p><strong>Scope & Impact:</strong></p><ul><li class=\"ql-indent-1\">Collaborate with multiple cross-functional global teams to deliver scalable data solutions.</li><li class=\"ql-indent-1\">Build scalable analytical frameworks to support product analytics for Uber's Customer Obsession Platform.</li><li class=\"ql-indent-1\">Partner with product managers, engineers, operations, and various data teams to help create and execute the product roadmap.</li><li class=\"ql-indent-1\">Scope the business problem.</li><li class=\"ql-indent-1\">Provide a data-driven perspective to product design.</li><li class=\"ql-indent-1\">Own the experimentation cycle to full global rollouts.</li><li class=\"ql-indent-1\">Impact estimation and long-term monitoring.</li><li class=\"ql-indent-1\">Proactively communicate insights and drive projects to achieve team goals.</li><li class=\"ql-indent-1\">Ensure data quality across critical pipelines and set up processes to triage data issues.</li><li class=\"ql-indent-1\">Build and maintain critical data pipelines, insight boards, and monitoring tools to track metrics and monitor platform health.</li><li class=\"ql-indent-1\">Identify opportunities to build new solutions to tackle customer and business pain points.</li></ul><h3 class=\"ql-indent-1\"><strong>Basic Qualifications:</strong></h3><ul><li class=\"ql-indent-1\">2+ years of work experience with a Bachelor's Degree OR 1+ years of work experience with a Master's Degree in a data-focused role such as:</li><li class=\"ql-indent-1\">Product analytics</li><li class=\"ql-indent-1\">Risk analytics</li><li class=\"ql-indent-1\">Business analytics</li><li class=\"ql-indent-1\">Business operations</li><li class=\"ql-indent-1\">Data science</li><li class=\"ql-indent-1\">Education in Engineering, Computer Science, Math, Economics, Statistics, or equivalent experience.</li><li class=\"ql-indent-1\">Proven competency in statistical languages like:</li><li class=\"ql-indent-1\">SQL</li><li class=\"ql-indent-1\">SAS</li><li class=\"ql-indent-1\">R</li><li class=\"ql-indent-1\">Modern programming languages like Python.</li><li class=\"ql-indent-1\">Past experience with a Product/Tech/Analytics Services company serving businesses with millions of customers across multiple platforms and countries.</li></ul><p></p>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}, {"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}, {"id": "a00eccc7-e123-4410-8387-9b6b6846ea43", "text": "SAS"}], "skills": [{"text": "R"}, {"text": "Analytics Services"}], "recruiterId": 3478550, "companyId": 1118906, "designations": ["Data Analyst"], "maximumSalaryINRFilter": 450000, "minimumSalaryINRFilter": 250000, "maximumSalaryINRMonthlyFilter": 37500, "minimumSalaryINRMonthlyFilter": 20833, "minimumExperienceFilter": 3, "maximumExperienceFilter": 6, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Uber", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "hideCompanyName": 0, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "We are Uber. The go-getters. The kind of people who are relentless about our mission to help people go anywhere and get anything and earn their way. Movement is what we power. It&rsquo;s our lifeblood. It runs through our veins. It&rsquo;s what gets us out of bed each morning. It pushes us to constantly reimagine how we can move better. For you. For all the places you want to go. For all the things you want to get. For all the ways you want to earn. Across the entire world. In real time. At the incredible speed of now. The idea for Uber was born on a snowy night in Paris in 2008, and ever since then our DNA of reimagination and reinvention carries on. We&rsquo;ve grown into a global platform powering flexible earnings and the movement of people and things in ever expanding ways. We&rsquo;ve gone from connecting rides on 4 wheels to 2 wheels to 18-wheel freight deliveries. From takeout meals to daily essentials to prescription drugs to just about anything you need at any time and earning your way. From drivers with background checks to real-time verification, safety is a top priority every single day. At Uber, the pursuit of reimagination is never finished, never stops, and is always just beginning.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": 1752488420000, "closedAt": 1757615400000, "activeJob": true, "earlyApplicant": false, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 217, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 250000, "maximumSalaryFilter": 450000}, {"id": "35484023", "score": ********, "jobId": 35484023, "kiwiJobId": "*********", "kiwiCompanyId": "1245586", "kiwiRecruiterId": "1526987", "title": "Data Scientist", "cleanedJobTitle": "data scientist", "company": {"companyId": 1119367, "name": "Teamware Solutions"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 5}, "maximumExperience": {"years": 7}, "minimumSalary": {"currency": "INR", "absoluteValue": 500000, "absoluteMonthlyValue": 41667}, "maximumSalary": {"currency": "INR", "absoluteValue": 700000, "absoluteMonthlyValue": 58333}, "postedAt": *************, "createdAt": *************, "updatedAt": 1752665112000, "industries": ["Information Technology"], "functions": ["Analytics/Business Intelligence"], "roles": ["Data Analyst"], "description": "<p>We are seeking a highly skilled <strong>Data Scientist</strong> with 5 to 7 years of experience to design, develop, and deploy data-driven solutions that solve complex business problems. The ideal candidate will be proficient in machine learning, statistical modeling, data engineering, and advanced analytics, with a strong understanding of business operations and a passion for turning data into actionable insights.</p><h3><strong>Key Responsibilities:</strong></h3><ul><li>Analyze large, complex datasets to identify patterns, trends, and insights that support business decision-making.</li><li>Design, build, and implement <strong>machine learning models</strong> and <strong>predictive analytics</strong> solutions.</li><li>Work with stakeholders to identify business opportunities and define data science use cases.</li><li>Translate business problems into analytical models and interpret the results for non-technical audiences.</li><li>Collaborate with data engineers to ensure scalable data pipelines and robust data infrastructure.</li><li>Conduct <strong>A/B testing</strong>, <strong>hypothesis testing</strong>, and <strong>statistical analysis</strong> to validate models and solutions.</li><li>Develop dashboards, visualizations, and reports to communicate results using tools like <strong>Power BI</strong>, <strong>Tableau</strong>, or <strong>Dash</strong>.</li><li>Ensure data quality, data governance, and ethical use of AI/ML models.</li><li>Stay current with the latest developments in data science, machine learning, and big data technologies.</li></ul><h3><strong>Required Skills & Qualifications:</strong></h3><ul><li>5 to 7 years of experience in <strong>data science</strong>, <strong>machine learning</strong>, or <strong>advanced analytics</strong>.</li><li>Proficiency in <strong>Python</strong> or <strong>R</strong> for data analysis and modeling.</li><li>Strong knowledge of machine learning libraries such as <strong>scikit-learn</strong>, <strong>TensorFlow</strong>, <strong>Keras</strong>, <strong>XGBoost</strong>, etc.</li><li>Solid understanding of <strong>statistics</strong>, <strong>probability</strong>, and <strong>data mining</strong> techniques.</li><li>Experience with <strong>SQL</strong> and working with relational databases.</li><li>Hands-on experience with big data technologies like <strong>Spark</strong>, <strong>Hadoop</strong>, or <strong>Databricks</strong> is a plus.</li><li>Familiarity with cloud platforms such as <strong>AWS</strong>, <strong>Azure</strong>, or <strong>Google Cloud Platform</strong>.</li><li>Strong problem-solving skills and the ability to work independently or collaboratively.</li><li>Excellent communication and storytelling skills, with the ability to explain technical concepts to non-technical stakeholders.</li></ul><h3><strong>Preferred Qualifications:</strong></h3><ul><li>Master's or PhD in Computer Science, Statistics, Mathematics, Data Science, or related field.</li><li>Experience in deploying models to production (MLOps) using tools like <strong>MLflow</strong>, <strong>Kubeflow</strong>, or <strong>Airflow</strong>.</li><li>Domain knowledge in <strong>finance</strong>, <strong>healthcare</strong>, <strong>retail</strong>, or <strong>telecom</strong> is a plus.</li><li>Experience with <strong>deep learning</strong>, <strong>NLP</strong>, or <strong>computer vision</strong> projects is desirable.</li></ul><p></p>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "b87f9211-f56e-4dd2-b7d1-386709c53e47", "text": "AWS"}, {"id": "80a1ee23-8b82-49d3-89ed-69c10d399ddd", "text": "Azure"}, {"id": "1c3431d1-457e-11e9-a89e-70106fbef856", "text": "Google Cloud"}, {"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}, {"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}], "skills": [{"text": "r"}], "recruiterId": 3479035, "companyId": 1119367, "designations": ["Data Analyst"], "maximumSalaryINRFilter": 700000, "minimumSalaryINRFilter": 500000, "maximumSalaryINRMonthlyFilter": 58333, "minimumSalaryINRMonthlyFilter": 41666, "minimumExperienceFilter": 5, "maximumExperienceFilter": 7, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Teamware Solutions", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "hideCompanyName": 0, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "Teamware Solutions, a business division of Quantum Leap Consulting Private Limited, offers cutting edge industry solutions for deriving business value for our clients' staffing initiatives. Offering deep domain expertise in Banking, Financial Services and Insurance, Oil and Gas, Infrastructure, Manufacturing, Retail, Telecom and Healthcare industries, Teamware leads its service in offering skills augmentation and professional consulting services.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": *************, "closedAt": *************, "activeJob": true, "earlyApplicant": false, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 37, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 500000, "maximumSalaryFilter": 700000}, {"id": "********", "score": ********, "jobId": ********, "kiwiJobId": "*********", "kiwiCompanyId": "1245586", "kiwiRecruiterId": "1526987", "title": "Data Scientist", "cleanedJobTitle": "data scientist", "company": {"companyId": 1119367, "name": "Teamware Solutions"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 5}, "maximumExperience": {"years": 6}, "minimumSalary": {"currency": "INR", "absoluteValue": 500000, "absoluteMonthlyValue": 41667}, "maximumSalary": {"currency": "INR", "absoluteValue": 700000, "absoluteMonthlyValue": 58333}, "postedAt": *************, "createdAt": *************, "updatedAt": *************, "industries": ["Information Technology"], "functions": ["Analytics/Business Intelligence"], "roles": ["Data Analyst"], "description": "<p>We are seeking a highly skilled <strong>Data Scientist</strong> with 5 to 7 years of experience to design, develop, and deploy data-driven solutions that solve complex business problems. The ideal candidate will be proficient in machine learning, statistical modeling, data engineering, and advanced analytics, with a strong understanding of business operations and a passion for turning data into actionable insights.</p><h3><strong>Key Responsibilities:</strong></h3><ul><li>Analyze large, complex datasets to identify patterns, trends, and insights that support business decision-making.</li><li>Design, build, and implement <strong>machine learning models</strong> and <strong>predictive analytics</strong> solutions.</li><li>Work with stakeholders to identify business opportunities and define data science use cases.</li><li>Translate business problems into analytical models and interpret the results for non-technical audiences.</li><li>Collaborate with data engineers to ensure scalable data pipelines and robust data infrastructure.</li><li>Conduct <strong>A/B testing</strong>, <strong>hypothesis testing</strong>, and <strong>statistical analysis</strong> to validate models and solutions.</li><li>Develop dashboards, visualizations, and reports to communicate results using tools like <strong>Power BI</strong>, <strong>Tableau</strong>, or <strong>Dash</strong>.</li><li>Ensure data quality, data governance, and ethical use of AI/ML models.</li><li>Stay current with the latest developments in data science, machine learning, and big data technologies.</li></ul><h3><strong>Required Skills & Qualifications:</strong></h3><ul><li>5 to 7 years of experience in <strong>data science</strong>, <strong>machine learning</strong>, or <strong>advanced analytics</strong>.</li><li>Proficiency in <strong>Python</strong> or <strong>R</strong> for data analysis and modeling.</li><li>Strong knowledge of machine learning libraries such as <strong>scikit-learn</strong>, <strong>TensorFlow</strong>, <strong>Keras</strong>, <strong>XGBoost</strong>, etc.</li><li>Solid understanding of <strong>statistics</strong>, <strong>probability</strong>, and <strong>data mining</strong> techniques.</li><li>Experience with <strong>SQL</strong> and working with relational databases.</li><li>Hands-on experience with big data technologies like <strong>Spark</strong>, <strong>Hadoop</strong>, or <strong>Databricks</strong> is a plus.</li><li>Familiarity with cloud platforms such as <strong>AWS</strong>, <strong>Azure</strong>, or <strong>Google Cloud Platform</strong>.</li><li>Strong problem-solving skills and the ability to work independently or collaboratively.</li><li>Excellent communication and storytelling skills, with the ability to explain technical concepts to non-technical stakeholders.</li></ul><h3><strong>Preferred Qualifications:</strong></h3><ul><li>Master's or PhD in Computer Science, Statistics, Mathematics, Data Science, or related field.</li><li>Experience in deploying models to production (MLOps) using tools like <strong>MLflow</strong>, <strong>Kubeflow</strong>, or <strong>Airflow</strong>.</li><li>Domain knowledge in <strong>finance</strong>, <strong>healthcare</strong>, <strong>retail</strong>, or <strong>telecom</strong> is a plus.</li><li>Experience with <strong>deep learning</strong>, <strong>NLP</strong>, or <strong>computer vision</strong> projects is desirable.</li></ul>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "b87f9211-f56e-4dd2-b7d1-386709c53e47", "text": "AWS"}, {"id": "80a1ee23-8b82-49d3-89ed-69c10d399ddd", "text": "Azure"}, {"id": "1c3431d1-457e-11e9-a89e-70106fbef856", "text": "Google Cloud"}, {"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}, {"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}], "skills": [{"text": "r"}], "recruiterId": 3479035, "companyId": 1119367, "designations": ["Data Analyst"], "maximumSalaryINRFilter": 700000, "minimumSalaryINRFilter": 500000, "maximumSalaryINRMonthlyFilter": 58333, "minimumSalaryINRMonthlyFilter": 41666, "minimumExperienceFilter": 5, "maximumExperienceFilter": 6, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Teamware Solutions", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "hideCompanyName": 0, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "Teamware Solutions, a business division of Quantum Leap Consulting Private Limited, offers cutting edge industry solutions for deriving business value for our clients' staffing initiatives. Offering deep domain expertise in Banking, Financial Services and Insurance, Oil and Gas, Infrastructure, Manufacturing, Retail, Telecom and Healthcare industries, Teamware leads its service in offering skills augmentation and professional consulting services.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": *************, "closedAt": *************, "activeJob": true, "earlyApplicant": false, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 38, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 500000, "maximumSalaryFilter": 700000}, {"id": "********", "score": ********, "jobId": ********, "kiwiJobId": "*********", "kiwiCompanyId": "1245765", "kiwiRecruiterId": "1527165", "title": "Data Scientist", "cleanedJobTitle": "data scientist", "company": {"companyId": 1119536, "name": "Neudesic Technologies Private Limited"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 5}, "maximumExperience": {"years": 10}, "minimumSalary": {"currency": "INR", "absoluteValue": 450000, "absoluteMonthlyValue": 37500}, "maximumSalary": {"currency": "INR", "absoluteValue": 750000, "absoluteMonthlyValue": 62500}, "postedAt": 1752729189000, "createdAt": 1752729189000, "updatedAt": 1752729188000, "industries": ["Information Technology"], "functions": ["Analytics/Business Intelligence"], "roles": ["Data Analyst"], "description": "<ul><li>Primary Skills: Python. SQL,ML,NLP,Data models,Data Insights</li><li>Strong mathematical skills to help collect, measure, organize and analyze data.</li><li>Knowledge of programming languages like SQL and Python</li><li>Technical proficiency regarding database design development, data models, techniques for data mining, and segmentation.</li><li>Proficiency in statistics and statistical packages like Excel to be used for data set analyzing.</li><li>Knowledge of data visualization software like PowerBI is desirable.</li><li>Knowledge of how to create and apply the most accurate algorithms to datasets in order to find solutions.</li><li>Problem-solving skills</li><li>Adept at queries, writing reports, and making presentations.</li><li>Team-working skills</li><li>Verbal and Written communication skills</li><li>Proven working experience in data analysis.</li><li>Expertise on a broad set of ML approaches and techniques, ranging from Artificial Neural Networks to Bayesian Non-Parametric methods, model preparation and selection (feature engineering, PCA, model assessment), and modeling techniques (optimization, simulation)</li><li>Proficiency in data analysis, modeling, and web services in Python. GPU programming experience.</li><li>Natural Language Processing experience  Ontology detection/Named Entity recognition and disambiguation and Predictive Analytics experience a plus.</li><li>Familiarity with existing ML stack (Azure Machine Learning, scikit-learn, Tensorflow, Keras and others)</li><li>SQL/NoSQL experience</li><li>Experience with Apache spark with Databricks or similar platform for crunching massive amount of data</li><li>Experience in leveraging AI in the CX (Customer Experience) domain a plus: Service (Topic Analysis, Aspect based sentiment analysis, NLP in Service context), Pre-Sales (Segmentation and Propensity Models) as well as Customer Success (Sentiment analysis, Best-Agent to Route, Churn Prediction, Customer Health Score, Recommender Systems for Next Best Action) using Machine Learning and Data Science for Recurring Revenue based business models</li></ul><p><strong>Software Development Skills</strong></p><ul><li>Experience in SQL and development experience in at least one scripting language (Python, Perl, etc.), and one high level programming language (Java)</li><li>Experience in containerized applications on cloud (Azure Kubernetes Service), cloud databases (Azure SQL), and data storage (Azure Data Lake Storage, File storage)</li></ul><p></p>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}, {"id": "189a96ef-457e-11e9-a89e-70106fbef856", "text": "Machine Learning"}, {"id": "18eb9da5-457e-11e9-a89e-70106fbef856", "text": "Data Visualization"}, {"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}, {"id": "7621f192-42ac-4dd0-8d45-c6cc54ae902e", "text": "Big Data"}, {"id": "57e3eb70-bab7-463b-b005-3b121ac8046a", "text": "Statistical Analysis"}, {"id": "6c9d68a4-baba-418a-8f9f-c691f9752c36", "text": "Cloud Computing"}], "recruiterId": 3479217, "companyId": 1119536, "designations": ["Data Analyst"], "maximumSalaryINRFilter": 750000, "minimumSalaryINRFilter": 450000, "maximumSalaryINRMonthlyFilter": 62500, "minimumSalaryINRMonthlyFilter": 37500, "minimumExperienceFilter": 5, "maximumExperienceFilter": 10, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Neudesic Technologies Private Limited", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "hideCompanyName": 0, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "Passion for technology drives us, but it&rsquo;s innovation that defines us. From design to development and support to management, Neudesic offers decades of experience, proven frameworks, and a disciplined approach to quickly deliver reliable, quality solutions that help our customers go to market faster.<br><br>What sets us apart from the rest is an amazing collection of people who live and lead with our core values. We believe that everyone should be Passionate about what they do, Disciplined to the core, Innovative by nature, committed to a Team and conduct themselves with Integrity. If these attributes mean something to you - we'd like to hear from you.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": 1752729189000, "closedAt": 1757874600000, "activeJob": true, "earlyApplicant": false, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 36, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 450000, "maximumSalaryFilter": 750000}, {"id": "35457915", "score": 8820000, "jobId": 35457915, "kiwiJobId": "*********", "kiwiCompanyId": "1243648", "kiwiRecruiterId": "1524713", "title": "Data Scientist", "cleanedJobTitle": "data scientist", "company": {"companyId": 1117465, "name": "Zentek Infosoft"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}, {"city": "Chennai", "country": "India", "state": "Tamil Nadu", "latLon": "13.082680,80.270718", "uuid": "b2b9a668-5339-4ea8-8b02-2f7e1a582556", "isoCode": "IN", "isdCode": 91, "stdCode": 44}, {"city": "Bengaluru / Bangalore", "country": "India", "state": "Karnataka", "latLon": "12.971599,77.594563", "uuid": "67bfdcae-92cb-4783-bf40-37f639e57710", "isoCode": "IN", "isdCode": 91, "stdCode": 80}], "minimumExperience": {"years": 10}, "maximumExperience": {"years": 12}, "minimumSalary": {"currency": "INR", "absoluteValue": 1000000, "absoluteMonthlyValue": 83333}, "maximumSalary": {"currency": "INR", "absoluteValue": 1200000, "absoluteMonthlyValue": 100000}, "postedAt": 1751629797000, "createdAt": 1751629797000, "updatedAt": 1751629797000, "industries": ["Information Technology"], "functions": ["Analytics/Business Intelligence"], "roles": ["Data Analyst"], "description": "<ul><li>Strong Python and intermediate SQL skills</li><li>Experience building, deploying, and evaluating ML/NLP/GenAI systems</li><li>Proficient in ML system design</li><li>Strong communication skills to articulate complex tasks into strategies</li><li>Familiarity with software engineering best practices including testing and OOP</li><li>Knowledge of cloud systems, especially Azure</li><li>Understanding of mathematical optimizers and how to configure them</li><li>Collaborate with data scientists, engineers, and business teams</li><li>Build scalable, reusable data science products with ML algorithms</li><li>Carry out data analyses for actionable business insights</li><li>Experience in prototyping and maintaining data science products (5+ years)</li><li>Applied data science knowledge across all lifecycle stages</li><li>Strong foundation in statistics and machine learning</li><li>Development experience in Python, Go, Java, or C++</li><li>Advanced SQL knowledge and experimental design understanding</li><li>Customer-centric approach focused on value delivery</li><li>Experience with ML system design like kNN models and NLP algorithms</li></ul><p></p>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}, {"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}, {"id": "80a1ee23-8b82-49d3-89ed-69c10d399ddd", "text": "Azure"}, {"id": "191083e8-457e-11e9-a89e-70106fbef856", "text": "Nlp"}, {"id": "265450ba-457e-11e9-a89e-70106fbef856", "text": "Knn"}, {"id": "18b040ff-457e-11e9-a89e-70106fbef856", "text": "Data Science"}], "skills": [{"text": "GenAI"}], "recruiterId": 3476477, "companyId": 1117465, "designations": ["Data Analyst"], "maximumSalaryINRFilter": 1200000, "minimumSalaryINRFilter": 1000000, "maximumSalaryINRMonthlyFilter": 100000, "minimumSalaryINRMonthlyFilter": 83333, "minimumExperienceFilter": 10, "maximumExperienceFilter": 12, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Zentek Infosoft", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "hideCompanyName": 0, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "Zentek Infosoft is a next-gen development and engineering solutions company, dedicated to crafting innovative answers that seamlessly blend data insights, unique user experience, and intelligent platforms to tackle modern problems. Within our portfolio, Votermood is a pioneering SaaS platform designed to empower citizens, political parties, and elected representatives. Serving as a digital gateway, Votermood cultivates inclusivity, collaboration, and transparency in the democratic process. Emploin, our AI-driven flagship product, revolutionizes Hire-to-Retire management. Seamlessly integrating workforce operations, unifying employee databases, and ensuring robust data protection, Emploin reshapes how businesses manage human capital and individuals navigate their career growth. Beyond our flagship products, Zentek empowers businesses with tailored digital solutions through Application Development, Project Management, Business Analysis, Testing, and Network/Infrastructure services. Our client-first and highly collaborative approach makes us a trusted partner for businesses looking to accelerate their digital journey with efficiency and excellence. At Zentek, we also offer staff augmentation services in the US and India, to help our clients meet their IT development requirements, ensuring excellence in every project.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": 1751629797000, "closedAt": 1756751400000, "activeJob": true, "earlyApplicant": false, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 40, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 1000000, "maximumSalaryFilter": 1200000}, {"id": "35457417", "score": 8820000, "jobId": 35457417, "kiwiJobId": "*********", "kiwiCompanyId": "1243648", "kiwiRecruiterId": "1524713", "title": "Data Scientist", "cleanedJobTitle": "data scientist", "company": {"companyId": 1117465, "name": "Zentek Infosoft"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 8}, "maximumExperience": {"years": 11}, "minimumSalary": {"currency": "INR", "absoluteValue": 600000, "absoluteMonthlyValue": 50000}, "maximumSalary": {"currency": "INR", "absoluteValue": 1150000, "absoluteMonthlyValue": 95833}, "postedAt": 1751612830000, "createdAt": 1751612830000, "updatedAt": 1751612829000, "industries": ["Software"], "functions": ["IT"], "roles": ["Business Analyst"], "description": "<ul><li>The candidate should at minimum show proficiency in:  Strong Python and intermediate SQL skills  Experience building, deploying, and evaluating ML/NLP/GenAI systems. </li><li> Experience with ML system design  Very strong (including written) communication skills and ability to break down and articulate complex tasks into implementation strategies</li><li>  Software engineering best practices, including testing, OOP, etc.  Knowledge of cloud systems, especially azure preferred. </li><li> A more traditional ML perspective, some knowledge and understanding of mathematical optimizers, how to select the best ones for purpose and set parameters Must Have: </li><li> Experience working closely with other data scientists, data engineers software engineers, data managers and business partners.  Can build scalable, re-usable, impactful data science products, usually containing statistical or machine learning algorithms, in collaboration with data engineers and software engineers. </li><li> Can carry out data analyses to yield actionable business insights.</li><li> Hands-on experience (typically 5+ years) designing, planning, prototyping, productionizing, maintaining and documenting reliable and scalable data science products in complex environments.  Applied knowledge of data science tools and approaches across all data lifecycle stages.</li><li> Thorough understanding of underlying mathematical foundations of statistics and machine learning. </li><li> Development experience in one or more object-oriented programming languages (e.g. Python, Go, Java, C++)  Advanced SQL knowledge.  Knowledge of experimental design and analysis.</li><li>  Customer-centric and pragmatic mindset. Focus on value delivery and swift execution, while maintaining attention to detail. Strong software engineering and ML system design experience. Experience with system designs like kNN model, NLP algorithms</li><li><br/></li></ul><p></p>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}, {"id": "8997ffdb-8967-49ed-8b2b-df6a17f05b48", "text": "Java"}, {"id": "4d4463cf-84c9-4112-a16c-b9a011fc11f7", "text": "C++"}, {"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}, {"id": "18dc70b8-457e-11e9-a89e-70106fbef856", "text": "Oop"}], "skills": [{"id": "66ceeda3-4a0b-11e9-a89e-70106fbef856", "text": "Go"}, {"text": "ML system"}], "recruiterId": 3476477, "companyId": 1117465, "designations": ["Business Analyst"], "maximumSalaryINRFilter": 1150000, "minimumSalaryINRFilter": 600000, "maximumSalaryINRMonthlyFilter": 95833, "minimumSalaryINRMonthlyFilter": 50000, "minimumExperienceFilter": 8, "maximumExperienceFilter": 11, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Zentek Infosoft", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "hideCompanyName": 0, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "Zentek Infosoft is a next-gen development and engineering solutions company, dedicated to crafting innovative answers that seamlessly blend data insights, unique user experience, and intelligent platforms to tackle modern problems. Within our portfolio, Votermood is a pioneering SaaS platform designed to empower citizens, political parties, and elected representatives. Serving as a digital gateway, Votermood cultivates inclusivity, collaboration, and transparency in the democratic process. Emploin, our AI-driven flagship product, revolutionizes Hire-to-Retire management. Seamlessly integrating workforce operations, unifying employee databases, and ensuring robust data protection, Emploin reshapes how businesses manage human capital and individuals navigate their career growth. Beyond our flagship products, Zentek empowers businesses with tailored digital solutions through Application Development, Project Management, Business Analysis, Testing, and Network/Infrastructure services. Our client-first and highly collaborative approach makes us a trusted partner for businesses looking to accelerate their digital journey with efficiency and excellence. At Zentek, we also offer staff augmentation services in the US and India, to help our clients meet their IT development requirements, ensuring excellence in every project.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": 1751612830000, "closedAt": 1756751400000, "activeJob": true, "earlyApplicant": false, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 24, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 600000, "maximumSalaryFilter": 1150000}, {"id": "35457906", "score": 8820000, "jobId": 35457906, "kiwiJobId": "*********", "kiwiCompanyId": "1243648", "kiwiRecruiterId": "1524713", "title": "Data Scientist", "cleanedJobTitle": "data scientist", "company": {"companyId": 1117465, "name": "Zentek Infosoft"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 10}, "maximumExperience": {"years": 12}, "minimumSalary": {"currency": "INR", "absoluteValue": 600000, "absoluteMonthlyValue": 50000}, "maximumSalary": {"currency": "INR", "absoluteValue": 1150000, "absoluteMonthlyValue": 95833}, "postedAt": 1751629625000, "createdAt": 1751629625000, "updatedAt": 1751629625000, "industries": ["Software"], "functions": ["IT"], "roles": ["Business Analyst"], "description": "<ul><li>Strong Python and intermediate SQL skills</li><li>Experience building, deploying, and evaluating ML/NLP/GenAI systems.</li><li>Experience with ML system design</li><li>Very strong (including written) communication skills and ability to break</li></ul><p>down and articulate complex tasks into implementation strategies</p><ul><li>Software engineering best practices, including testing, OOP, etc.</li><li>Knowledge of cloud systems, especially azure preferred.</li><li>A more traditional ML perspective, some knowledge and understanding of</li></ul><p>mathematical optimizers, how to select the best ones for purpose and set</p><p>parameters</p><p>Must Have:</p><ul><li>Experience working closely with other data scientists, data engineers</li></ul><p>software engineers, data managers and business partners.</p><ul><li>Can build scalable, re-usable, impactful data science products, usually</li></ul><p>containing statistical or machine learning algorithms, in collaboration with data</p><p>engineers and software engineers.</p><ul><li>Can carry out data analyses to yield actionable business insights.</li><li>Hands-on experience (typically 5+ years) designing, planning, prototyping,</li></ul><p>productionizing, maintaining and documenting reliable and scalable data science</p><p>products in complex environments.</p><ul><li>Applied knowledge of data science tools and approaches across all data</li></ul><p>lifecycle stages.</p><ul><li>Thorough understanding of underlying mathematical foundations of</li></ul><p>statistics and machine learning.</p><ul><li>Development experience in one or more object-oriented programming</li></ul><p>languages (e.g. Python, Go, Java, C++)</p><ul><li>Advanced SQL knowledge.</li><li>Knowledge of experimental design and analysis.</li><li>Customer-centric and pragmatic mindset. Focus on value delivery and swift</li></ul><p>execution, while maintaining attention to detail.</p><p>Strong software engineering and ML system design experience. Experience with</p><p>system designs like kNN model, NLP algorithms.</p>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}, {"id": "8997ffdb-8967-49ed-8b2b-df6a17f05b48", "text": "Java"}, {"id": "4d4463cf-84c9-4112-a16c-b9a011fc11f7", "text": "C++"}, {"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}], "skills": [{"id": "66ceeda3-4a0b-11e9-a89e-70106fbef856", "text": "Go"}, {"text": "NLP algorithms"}], "recruiterId": 3476477, "companyId": 1117465, "designations": ["Business Analyst"], "maximumSalaryINRFilter": 1150000, "minimumSalaryINRFilter": 600000, "maximumSalaryINRMonthlyFilter": 95833, "minimumSalaryINRMonthlyFilter": 50000, "minimumExperienceFilter": 10, "maximumExperienceFilter": 12, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Zentek Infosoft", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "hideCompanyName": 0, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "Zentek Infosoft is a next-gen development and engineering solutions company, dedicated to crafting innovative answers that seamlessly blend data insights, unique user experience, and intelligent platforms to tackle modern problems. Within our portfolio, Votermood is a pioneering SaaS platform designed to empower citizens, political parties, and elected representatives. Serving as a digital gateway, Votermood cultivates inclusivity, collaboration, and transparency in the democratic process. Emploin, our AI-driven flagship product, revolutionizes Hire-to-Retire management. Seamlessly integrating workforce operations, unifying employee databases, and ensuring robust data protection, Emploin reshapes how businesses manage human capital and individuals navigate their career growth. Beyond our flagship products, Zentek empowers businesses with tailored digital solutions through Application Development, Project Management, Business Analysis, Testing, and Network/Infrastructure services. Our client-first and highly collaborative approach makes us a trusted partner for businesses looking to accelerate their digital journey with efficiency and excellence. At Zentek, we also offer staff augmentation services in the US and India, to help our clients meet their IT development requirements, ensuring excellence in every project.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": 1751629625000, "closedAt": 1756751400000, "activeJob": true, "earlyApplicant": false, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 16, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 600000, "maximumSalaryFilter": 1150000}, {"id": "35466347", "score": 8820000, "jobId": 35466347, "kiwiJobId": "*********", "kiwiCompanyId": "1070878", "kiwiRecruiterId": "1363577", "title": "Data Scientist", "cleanedJobTitle": "data scientist", "company": {"companyId": 1066994, "name": "COTIVITI", "logo": "https://media.monsterindia.com/logos/xeft_cotivitinx/jdlogo.gif"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}, {"city": "Pune", "country": "India", "state": "Maharashtra", "latLon": "18.520430,73.856744", "uuid": "0fb3f865-7da3-424a-8f14-a0d9db705ec1", "isoCode": "IN", "isdCode": 91, "stdCode": 22}], "minimumExperience": {"years": 5}, "maximumExperience": {"years": 7}, "minimumSalary": {"currency": "INR", "absoluteValue": 500000, "absoluteMonthlyValue": 41667}, "maximumSalary": {"currency": "INR", "absoluteValue": 700000, "absoluteMonthlyValue": 58333}, "postedAt": 1751954623000, "createdAt": 1751954623000, "updatedAt": 1751954623000, "industries": ["Information Technology"], "functions": ["Analytics/Business Intelligence"], "roles": ["Data Analyst"], "description": "<p><PERSON><PERSON><PERSON><PERSON> is seeking a skilled <strong>Data Scientist</strong> to uncover valuable insights from vast amounts of data, driving smarter decisions and enhancing our products. This role offers the opportunity to apply data mining techniques, statistical analysis, and build high-quality prediction systems integrated with our innovative healthcare solutions. You will work on building value-oriented, production-level machine learning solutions in Healthcare Technology to reduce healthcare costs and improve health quality and outcomes.</p><p><strong>Job Responsibilities:</strong></p><ul><li>Engage with customers to identify opportunities to integrate data/insights into decision-making.</li><li>Understand and translate business requirements from internal and external stakeholders into data product and features.</li><li>Process, cleanse, and verify the integrity of data used for analysis.</li><li>Conduct exploratory data analysis to verify the initial hypothesis associated with potential AI/ML use cases.</li><li>Prepare final trained models and develop validation test sets for QA.</li><li>Work with production operations to deploy models and monitor model performance.</li><li>Apply statistical methodology to solve business problems; appropriately interprets meaning from results.</li><li>Contribute to data product discovery by developing business case analytics, user stories, conducting exploratory analyses, and prototyping ideas/analytics.</li><li>Prepare project documentation, and conduct end-user training.</li><li>Conduct on-demand analyses to support key internal and client-facing deliverables.</li><li>Cultivate and share best practices in structured problem solving and quantitative/qualitative analysis.</li><li>Complete all responsibilities as outlined on annual Performance Plan.</li></ul><p><strong>About You:</strong></p><ul><li>Should hold a Bachelor's or Master's degree in data/analytics, computer science, engineering, economics, mathematics, or statistics.</li><li>Should have experience using machine learning tools to develop production-strength models including, but not limited to, Python, TensorFlow, Keras, pandas, numpy, scikit-learn.</li><li>Should have a deep understanding of supervised and unsupervised algorithms.</li><li>Should have experience extracting and transforming source data using SQL, Spark, Scala, Hive, Impala.</li><li>Should be able to package insights in highly consumable and flexible formats for a variety of audiences.</li><li>Should be able to work well independently and with cross-functional, matrixed teams in an agile environment.</li><li>Should be able to handle multiple tasks, prioritize, and meet deadlines.</li><li>Experience in healthcare analytics, consulting, technology/services, product development, or other relevant roles will be an advantage.</li><li>Should develop and maintain positive working relationships with others.</li><li>Should share ideas and information and assist colleagues unprompted.</li><li>Should take pride in the achievement of team objectives.</li><li>Should have credibility with the team and senior managers.</li><li>Should be self-motivated and driven to achieve results.</li><li>Should work with a sense of urgency.</li><li>Should have a high customer service ethic and be passionate about meeting customer expectations.</li><li>Should keep pace with change and acquire knowledge/skills as the business evolves.</li><li>Should handle confidential information with sensitivity.</li><li>Should think for the benefit of the organization and team.</li><li>Should demonstrate independent thinking and come up with approaches on different aspects of work.</li><li>Should be a role model for the team and a subject matter expert of the associated domain.</li><li>Should exhibit behaviors consistent with Cotiviti Values: Open, Customer Driven, Accountable, Collaborative.</li></ul>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}, {"id": "1b4f8a41-457e-11e9-a89e-70106fbef856", "text": "Tensorflow"}, {"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}], "skills": [{"text": "machine learning tools"}, {"id": "0df9640f-5453-11e9-a89e-70106fbef856", "text": "healthcare analytics"}, {"id": "64bbbd3e-4a0b-11e9-a89e-70106fbef856", "text": "Consulting"}], "recruiterId": 2355288, "companyId": 1066994, "designations": ["Data Analyst"], "maximumSalaryINRFilter": 700000, "minimumSalaryINRFilter": 500000, "maximumSalaryINRMonthlyFilter": 58333, "minimumSalaryINRMonthlyFilter": 41666, "minimumExperienceFilter": 5, "maximumExperienceFilter": 7, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "COTIVITI", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "companyLogoUrl": "https://media.monsterindia.com/logos/xeft_cotivitinx/jdlogo.gif", "hideCompanyName": 0, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "companyProfile": "Cotiviti enables healthcare organizations to deliver better care at lower cost through advanced technology and data analytics, helping to ensure the quality and sustainability of how healthcare is delivered in the United States. Cotiviti&rsquo;s solutions are a critical foundation for healthcare payers in their mission to lower healthcare costs and improve quality through higher performing  payment accuracy, quality improvement, risk adjustment, and consumer engagement programs. The company also supports the retail industry with data management and recovery audit services that improve business outcomes. For more information, visit www.cotiviti.com.", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": 1751954623000, "closedAt": 1757097000000, "activeJob": true, "earlyApplicant": false, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 95, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 500000, "maximumSalaryFilter": 700000}, {"id": "35463623", "score": 8820000, "jobId": 35463623, "kiwiJobId": "*********", "kiwiCompanyId": "1243838", "kiwiRecruiterId": "1524909", "title": "Data Scientist", "cleanedJobTitle": "data scientist", "company": {"companyId": 1117648, "name": "Webkit24"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}, {"city": "Chennai", "country": "India", "state": "Tamil Nadu", "latLon": "13.082680,80.270718", "uuid": "b2b9a668-5339-4ea8-8b02-2f7e1a582556", "isoCode": "IN", "isdCode": 91, "stdCode": 44}, {"city": "Pune", "country": "India", "state": "Maharashtra", "latLon": "18.520430,73.856744", "uuid": "0fb3f865-7da3-424a-8f14-a0d9db705ec1", "isoCode": "IN", "isdCode": 91, "stdCode": 22}], "minimumExperience": {"years": 5}, "maximumExperience": {"years": 9}, "minimumSalary": {"currency": "INR", "absoluteValue": 400000, "absoluteMonthlyValue": 33333}, "maximumSalary": {"currency": "INR", "absoluteValue": 700000, "absoluteMonthlyValue": 58333}, "postedAt": *************, "createdAt": *************, "updatedAt": *************, "industries": ["Digital Marketing"], "functions": ["Analytics/Business Intelligence"], "roles": ["Data Analyst"], "description": "<p><strong>Key Responsibilities</strong></p><ul><li>Analyze large datasets to discover trends and patterns</li><li>Build predictive models and machine-learning algorithms</li><li>Collaborate with product, engineering, and marketing teams to implement data-driven strategies</li><li>Identify opportunities for data acquisition and automation of data processes</li><li>Create clear visualizations and dashboards using tools like Power BI, Tableau, or Python libraries</li><li>Maintain documentation of processes, models, and project results</li></ul><p><strong>Required Skills & Qualifications</strong></p><ul><li>Bachelors or Masters in Computer Science, Statistics, Mathematics, or related field</li><li>25 years of experience as a Data Scientist or similar role</li><li>Proficiency in Python or R for statistical analysis and machine learning</li><li>Strong knowledge of SQL for data querying</li><li>Familiarity with cloud platforms (AWS, GCP, or Azure) is a plus</li><li>Excellent problem-solving, critical thinking, and communication skills</li><li>Preferred Qualifications</li><li>Experience with NLP, Deep Learning, or AI model deployment</li><li>Hands-on experience with frameworks like Scikit-learn, TensorFlow, or PyTorch</li><li>Experience working in remote or distributed teams</li></ul>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "b87f9211-f56e-4dd2-b7d1-386709c53e47", "text": "AWS"}, {"id": "1ba65d0c-457e-11e9-a89e-70106fbef856", "text": "Gcp"}, {"id": "80a1ee23-8b82-49d3-89ed-69c10d399ddd", "text": "Azure"}, {"id": "4a5d94f6-424c-4df8-adaf-700135c94dab", "text": "Sql"}], "recruiterId": 3476710, "companyId": 1117648, "designations": ["Data Analyst"], "maximumSalaryINRFilter": 700000, "minimumSalaryINRFilter": 400000, "maximumSalaryINRMonthlyFilter": 58333, "minimumSalaryINRMonthlyFilter": 33333, "minimumExperienceFilter": 5, "maximumExperienceFilter": 9, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "Webkit24", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "hideCompanyName": 0, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": *************, "closedAt": *************, "activeJob": true, "earlyApplicant": false, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 100, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 400000, "maximumSalaryFilter": 700000}, {"id": "********", "score": 6299999.5, "jobId": ********, "kiwiJobId": "*********", "kiwiCompanyId": "1070878", "kiwiRecruiterId": "1363577", "title": "Data Scientist - AI/ML", "cleanedJobTitle": "data scientist ai ml", "company": {"companyId": 1066994, "name": "COTIVITI", "logo": "https://media.monsterindia.com/logos/xeft_cotivitinx/jdlogo.gif"}, "locations": [{"city": "Hyderabad / Secunderabad, Telangana", "country": "India", "state": "Telangana", "latLon": "17.439930,78.498274", "uuid": "72da2468-1cbc-4a9a-863a-89e2cfd6502f", "isoCode": "IN", "isdCode": 91, "stdCode": 40}], "minimumExperience": {"years": 5}, "maximumExperience": {"years": 8}, "minimumSalary": {"currency": "INR", "absoluteValue": 100000, "absoluteMonthlyValue": 8333}, "maximumSalary": {"currency": "INR", "absoluteValue": 100000, "absoluteMonthlyValue": 8333}, "postedAt": 1742981314000, "createdAt": 1742981314000, "updatedAt": 1751953838000, "functions": ["Others"], "roles": ["Other Roles"], "description": "<p><strong>Position Overview:</strong></p><p></p><p>As a Senior Software Engineer on the AI Engineering Team at Cotiviti, you will be a leading force in developing robust, scalable machine learning solutions for healthcare applications. This senior-level position involves significant responsibility, including leading design and development efforts, mentoring junior engineers, and ensuring the delivery of high-quality solutions.</p><p></p><p><strong>Basic Qualifications:</strong></p><p></p><ul><li>Bachelors degree in Computer Science, Engineering, Math, or a related field, or equivalent experience</li><li>7+ years experience of developing AI/ML solutions.</li><li>Strong programming skills in languages such as Python, C#, Java, machine learning frameworks such as TensorFlow, Keras, or PyTorch.</li><li>Extensive background in data mining and statistical analysis/predictive analytics.</li><li>Ability to solve problems with analytical thinking and the ability to conceptualize, design, and build models from scratch. Hands-on experience in defining and designing the models for predictive analytics based on business requirements.</li><li>Deep understanding of data architecture principles, have good experience with different data sources and databases like Oracle, MS SQL Server, HDFS</li><li>Should have experience in Big Data technologies to support the Model execution in production.</li><li>Knowledge of CI/CD pipelines for production deployment of models. Hands-on experience with ML Ops is an added advantage.</li><li>Understanding in Agile, SDLC.</li><li>Strong analytical and problem-solving skills.</li><li>Familiarity with Agile processes and tools like Jira and Confluence</li><li>Strong drive to learn and advocate for development best practices, must be a team player with the ability to manage multiple tasks in a deadline driven environment</li><li>Knowledge in the Healthcare Insurance sector is an added advantage.</li></ul><p><strong>Responsibilities:</strong></p><p></p><ul><li>Lead the development and implementation of machine learning solutions for healthcare applications</li><li>Guide and mentor a team of developers and testers</li><li>Collaborate with data scientists and other engineers to design and build scalable solutions</li><li>Write, test, and maintain high-quality code along with Code coverage</li><li>Lead design and code review sessions</li><li>Troubleshoot and resolve complex technical issues</li><li>Document your work and share knowledge with the team</li><li>Advocate for and implement development best practices</li><li>Train and mentor junior engineers and software engineers</li></ul><p></p>", "jobTypes": ["Permanent Job"], "employmentTypes": ["Full time"], "itSkills": [{"id": "1b4f8a41-457e-11e9-a89e-70106fbef856", "text": "Tensorflow"}, {"id": "8997ffdb-8967-49ed-8b2b-df6a17f05b48", "text": "Java"}, {"id": "e080169f-056f-46af-84fb-0dc7e16ebe6a", "text": "Agile"}, {"id": "1c463431-457e-11e9-a89e-70106fbef856", "text": "<PERSON><PERSON>"}, {"id": "eb81df54-a693-4ef7-b44f-ded7e12e1d7c", "text": "Python"}, {"id": "10a890fd-326e-4949-90a2-4d938d6f5f2f", "text": "Sdlc"}], "recruiterId": 2355288, "companyId": 1066994, "designations": ["Other Roles"], "maximumSalaryINRFilter": 100000, "minimumSalaryINRFilter": 100000, "maximumSalaryINRMonthlyFilter": 8333, "minimumSalaryINRMonthlyFilter": 8333, "minimumExperienceFilter": 5, "maximumExperienceFilter": 8, "questionnaire": false, "employerTypes": ["Company"], "walkInVenue": {}, "companyName": "COTIVITI", "status": 1, "accountId": 0, "currencyCode": "INR", "hideSalary": 1, "companyLogoUrl": "https://media.monsterindia.com/logos/xeft_cotivitinx/jdlogo.gif", "hideCompanyName": 0, "renewalAt": *************, "autoMatch": 0, "showContactDetails": 0, "jobSource": "ORGANIC", "postingBoard": "SINGLE", "graceJob": 0, "quickJob": 0, "jobFrom": "MOIN_MOHQ", "isoCode": "en", "channelId": 1, "channelName": "India", "nationalities": [{"uuid": "********-fc81-11e8-92d8-000c290b6677", "text": "Indian", "isoCode": "en", "enabled": 1}], "isBold": 1, "isJdLogo": 1, "isSearchLogo": 1, "siteContext": "<PERSON><PERSON><PERSON><PERSON>", "freshness": *************, "closedAt": *************, "activeJob": true, "isCJT": 0, "isMicrosite": 0, "redirectStage": 0, "totalApplicants": 42, "isGptwCompany": 0, "jobSalaryConfidential": true, "isUrgentlyHiring": false, "quickApplyJob": 1, "jobClassification": "ORGANIC", "isMarquee": 1, "isTestJob": false, "minimumSalaryFilter": 100000, "maximumSalaryFilter": 100000}], "meta": {"paging": {"cursors": {"next": "20", "previous": "0"}, "total": 83, "limit": 20}, "resultId": "e86ae8dd-b2cd-47c4-ad12-23f2c52895db", "version": "DEFAULT", "buildVersion": "0.0.1"}, "spellCheckApplied": false, "filter": [{"filter": "roles", "label": "Role", "values": [{"name": "Data Analyst", "count": 51}, {"name": "Software Engineer/Programmer", "count": 13}, {"name": "Other Analytics/Business Intelligence", "count": 11}, {"name": "Business Analyst", "count": 9}, {"name": "Other Roles", "count": 6}, {"name": "Database Administrator (DBA)", "count": 2}, {"name": "Systems Engineer", "count": 2}, {"name": "CEO/MD/Country Manager", "count": 1}, {"name": "Database Architect/Designer", "count": 1}, {"name": "Datawarehousing Consultants", "count": 1}, {"name": "Financial Analyst", "count": 1}, {"name": "Marketing Analyst", "count": 1}, {"name": "Research Scientist", "count": 1}, {"name": "System Analyst/Tech Architect", "count": 1}]}, {"filter": "industries", "label": "Industry", "values": [{"name": "Information Technology", "count": 17}, {"name": "Biotechnology", "count": 10}, {"name": "Information Services", "count": 9}, {"name": "Software", "count": 8}, {"name": "Consulting", "count": 7}, {"name": "Pharmaceutical", "count": 7}, {"name": "Other", "count": 6}, {"name": "Software Engineering", "count": 5}, {"name": "Biopharma", "count": 3}, {"name": "Manufacturing", "count": 3}, {"name": "Oil and Gas", "count": 3}, {"name": "Business Intelligence", "count": 2}, {"name": "Recruiting", "count": 2}, {"name": "Recruitment/Staffing/RPO", "count": 2}, {"name": "Staffing Agency", "count": 2}, {"name": "Transportation", "count": 2}, {"name": "Digital Marketing", "count": 1}, {"name": "Enterprise Applications", "count": 1}, {"name": "IT Management", "count": 1}, {"name": "Information and Communications Technology (ICT)", "count": 1}, {"name": "Medical Device", "count": 1}, {"name": "Sports", "count": 1}]}, {"filter": "functions", "label": "Function", "values": [{"name": "analytics/business intelligence", "count": 52}, {"name": "it", "count": 24}, {"name": "others", "count": 6}, {"name": "data science", "count": 5}, {"name": "data science / ai / machine learning", "count": 1}, {"name": "data science and engineering", "count": 1}, {"name": "manufacturing/engineering/r&d", "count": 1}, {"name": "technology", "count": 1}]}, {"filter": "jobTypes", "label": "Job Type", "values": [{"name": "Permanent Job", "count": 83}]}, {"filter": "qualifications", "label": "Qualification", "values": [{"name": "Bachelor Of Technology (B.Tech/B.E)", "count": 1}]}, {"filter": "employerTypes", "label": "Employer Type", "values": [{"name": "Company", "count": 81}, {"name": "Consultant", "count": 2}]}, {"filter": "salaryRanges", "label": "Salary", "values": [{"name": "600000~*", "count": 57}, {"name": "400000~*", "count": 44}, {"name": "800000~*", "count": 42}, {"name": "1000000~*", "count": 29}, {"name": "1200000~*", "count": 20}, {"name": "200000~*", "count": 11}, {"name": "0~*", "count": 8}, {"name": "1400000~*", "count": 5}, {"name": "1600000~*", "count": 4}, {"name": "1800000~*", "count": 4}, {"name": "2000000~*", "count": 1}]}, {"filter": "experienceRanges", "label": "Experience (in years)", "values": [{"name": "6~6", "count": 56}, {"name": "5~5", "count": 55}, {"name": "7~7", "count": 46}, {"name": "8~8", "count": 43}, {"name": "9~9", "count": 37}, {"name": "4~4", "count": 36}, {"name": "3~3", "count": 29}, {"name": "10~10", "count": 26}, {"name": "11~11", "count": 16}, {"name": "12~12", "count": 14}, {"name": "2~2", "count": 13}, {"name": "1~1", "count": 9}, {"name": "13~13", "count": 6}, {"name": "14~14", "count": 4}, {"name": "15~15", "count": 4}, {"name": "0~0", "count": 3}, {"name": "15~*", "count": 2}, {"name": "16~16", "count": 2}]}, {"filter": "jobFreshness", "label": "Job Freshness", "values": [{"name": "30", "count": 35}, {"name": "15", "count": 14}, {"name": "7", "count": 9}, {"name": "3", "count": 1}]}, {"filter": "companies", "label": "Top Companies", "values": [{"name": "Amgen Technology Private Limited", "count": 18}, {"name": "Grid Dynamics Private Limited", "count": 6}, {"name": "Alike Thoughts", "count": 3}, {"name": "Worleyparsons India Private Limited", "count": 3}, {"name": "Zentek Infosoft", "count": 3}, {"name": "Blend360 India", "count": 2}, {"name": "COTIVITI", "count": 2}, {"name": "Fusion Plus Solutions", "count": 2}, {"name": "GlobalData Publications Inc", "count": 2}, {"name": "IBM", "count": 2}, {"name": "PHOTON", "count": 2}, {"name": "Sampoorna Consultants Private Limited", "count": 2}, {"name": "Systechcorp Private Limited", "count": 2}, {"name": "Teamware Solutions", "count": 2}, {"name": "Tiger Analytics", "count": 2}, {"name": "Uber", "count": 2}, {"name": "ValueLabs LLP", "count": 2}, {"name": "Adecco India Private Limited", "count": 1}, {"name": "AiFA Labs", "count": 1}, {"name": "Amazon Development Centre (India) Private Limited", "count": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "count": 1}, {"name": "Blend", "count": 1}, {"name": "CareerNet Technologies Private Limited", "count": 1}, {"name": "DAZN", "count": 1}, {"name": "Fort Technologies", "count": 1}, {"name": "Google Inc", "count": 1}, {"name": "Innominds Software Inc", "count": 1}, {"name": "Ivy", "count": 1}, {"name": "Medtronic", "count": 1}, {"name": "Neudesic Technologies Private Limited", "count": 1}, {"name": "Robotics Technologies", "count": 1}, {"name": "Siemens", "count": 1}, {"name": "Soul Ai", "count": 1}, {"name": "Sp Staffing", "count": 1}, {"name": "Stack Digital", "count": 1}, {"name": "Team Geek Solutions", "count": 1}, {"name": "The It Mind Services", "count": 1}, {"name": "Ti Steps", "count": 1}, {"name": "Verified Group Bounteous X Accolite", "count": 1}, {"name": "Webkit24", "count": 1}, {"name": "YASH Technologies", "count": 1}, {"name": "Zensar Technologies Limited", "count": 1}, {"name": "Zetwerk", "count": 1}]}, {"filter": "jobStates", "label": "State", "values": [{"name": "Telangana", "count": 83}, {"name": "Tamil Nadu", "count": 15}, {"name": "Karnataka", "count": 13}, {"name": "Maharashtra", "count": 10}, {"name": "Madhya Pradesh", "count": 1}, {"name": "West Bengal", "count": 1}]}, {"filter": "jobCountries", "label": "Country", "values": [{"name": "India", "count": 83}]}, {"filter": "isGptwCompany", "label": "Great Place to Work", "values": []}, {"filter": "postedBy", "label": "Posted by", "values": [{"name": "Company", "count": 81}, {"name": "Consultant", "count": 2}]}, {"filter": "title", "label": "Job Title", "values": [{"name": "Data Scientist", "count": 33}, {"name": "Senior Data Scientist", "count": 8}, {"name": "MDM Data Scientist", "count": 4}, {"name": "MDM Senior Data Scientist", "count": 3}, {"name": "Data Scientist-Artificial Intelligence", "count": 2}, {"name": "Junior Data Scientist", "count": 2}, {"name": "Lead Data Scientist", "count": 2}, {"name": "Staff Data Scientist", "count": 2}, {"name": "Associate Data Scientist", "count": 1}, {"name": "Associate Principal - Data Scientist", "count": 1}, {"name": "Data Scientist (US Value & Access Insights)", "count": 1}, {"name": "Data Scientist - AI Solutions For Electrification", "count": 1}, {"name": "Data Scientist - AI/ML", "count": 1}, {"name": "Data Scientist - BLR", "count": 1}, {"name": "Data Scientist 2", "count": 1}, {"name": "Data Scientist I", "count": 1}, {"name": "Data Scientist II", "count": 1}, {"name": "Data Scientist – Generative AI & LLM", "count": 1}, {"name": "Data Scientist, ISS", "count": 1}, {"name": "Data Scientist, gTech Ads Solutions", "count": 1}, {"name": "Data Scientist- ( Lead)", "count": 1}, {"name": "Data Scientist/Senior Data Scientist", "count": 1}, {"name": "Data scientist With snoflakes", "count": 1}, {"name": "Gen AI | ML- Data Scientist- ( Lead) -Mum/chennai", "count": 1}, {"name": "Lead Data Scientist (Gen AI)", "count": 1}, {"name": "Lead Data Scientist - Media Mix Modelling (MMM)", "count": 1}, {"name": "Principal Data Scientist", "count": 1}, {"name": "Senior Analyst/Data Scientist - NLP/GenAI", "count": 1}, {"name": "Senior Associate Data Scientist", "count": 1}, {"name": "Senior Data Scientist Engineer", "count": 1}, {"name": "Senior/Lead Data Scientist (Gen AI)", "count": 1}, {"name": "Sr. Data Scientist", "count": 1}, {"name": "<PERSON>. Data Scientist- Gen AI (AI/ML)", "count": 1}, {"name": "Sr. Principal Data Scientist - AI/ML Job", "count": 1}, {"name": "Subcon demand For Apple project - Data scientist", "count": 1}]}, {"filter": "quickApply<PERSON>ob", "label": "Quick Apply", "values": [{"name": "Quick Apply", "count": 75}]}, {"filter": "jobCities", "label": "City", "values": [{"name": "Hyderabad / Secunderabad, Telangana", "count": 83, "selected": true}, {"name": "Chennai", "count": 15}, {"name": "Bengaluru / Bangalore", "count": 13}, {"name": "Pune", "count": 9}, {"name": "Remote", "count": 2}, {"name": "Indore", "count": 1}, {"name": "Kolkata", "count": 1}, {"name": "Mumbai", "count": 1}]}, {"filter": "locations", "label": "Location", "sections": [{"sectionName": "Cities", "sectionType": "cities", "values": [{"name": "Hyderabad / Secunderabad, Telangana", "count": 83, "selected": true, "uiName": "Hyderabad"}, {"name": "Chennai", "count": 15, "uiName": "Chennai"}, {"name": "Bengaluru / Bangalore", "count": 13, "uiName": "Bengaluru"}, {"name": "Pune", "count": 9, "uiName": "Pune"}, {"name": "Remote", "count": 2, "uiName": "Remote"}, {"name": "Indore", "count": 1, "uiName": "Indore"}, {"name": "Kolkata", "count": 1, "uiName": "Kolkata"}, {"name": "Mumbai", "count": 1, "uiName": "Mumbai"}]}, {"sectionName": "States", "sectionType": "states", "values": [{"name": "Telangana", "count": 83}, {"name": "Tamil Nadu", "count": 15}, {"name": "Karnataka", "count": 13}, {"name": "Maharashtra", "count": 10}, {"name": "Madhya Pradesh", "count": 1}, {"name": "West Bengal", "count": 1}]}]}], "spellCheck": {"originalText": "data scientist", "suggestion": "", "showingResultsFor": "data scientist"}}