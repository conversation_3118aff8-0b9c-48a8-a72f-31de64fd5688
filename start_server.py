#!/usr/bin/env python3
"""
Simple server startup script for the Naukri API scraper
"""

import uvicorn
from naukri_api_scraper import app

if __name__ == "__main__":
    print("Starting Naukri API Scraper server...")
    print("Server will be available at: http://localhost:8004")
    print("API Documentation: http://localhost:8004/docs")
    print("Health Check: http://localhost:8004/health")
    print("\nEndpoints:")
    print("  POST /scrape - Scrape jobs from Naukri.com")
    print("  GET  /health - Health check")
    print("\nPress Ctrl+C to stop the server")
    print("-" * 50)
    
    uvicorn.run(app, host="0.0.0.0", port=8004)
